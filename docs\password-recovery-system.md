# Sistema de Recuperação de Senha - Smar AssetView Nx

## Visão Geral

O sistema de recuperação de senha do Smar AssetView Nx permite que usuários redefinam suas senhas de forma segura através de um link enviado por e-mail. O sistema implementa múltiplas camadas de segurança, incluindo rate limiting, tokens criptograficamente seguros e logs de auditoria.

## Arquitetura

### Componentes Principais

1. **AuthService** - Gerencia operações de autenticação
2. **PasswordResetTokenService** - Gerencia tokens de recuperação
3. **EmailService** - Envia e-mails de recuperação
4. **AuditService** - Registra eventos de segurança
5. **PasswordPolicyService** - Valida políticas de senha

### Fluxo de Recuperação

```mermaid
sequenceDiagram
    participant U as Usuário
    participant W as Web App
    participant A as AuthService
    participant T as TokenService
    participant E as EmailService
    participant D as Database

    U->>W: Solicita recuperação (/forgot-password)
    W->>A: ForgotPasswordAsync(email)
    A->>D: Busca usuário por email
    A->>T: CanRequestTokenAsync(userId)
    T->>D: Verifica rate limiting
    A->>T: GenerateTokenAsync(userId)
    T->>D: Salva token
    A->>E: SendPasswordResetEmailAsync()
    E->>U: Envia e-mail com link
    U->>W: Clica no link (/reset-password?token=...)
    W->>A: ValidateResetTokenAsync(token)
    A->>T: ValidateTokenAsync(token)
    T->>D: Verifica token válido
    U->>W: Define nova senha
    W->>A: ResetPasswordAsync(request)
    A->>T: ConsumeTokenAsync(token)
    T->>D: Marca token como usado
    A->>D: Atualiza senha do usuário
```

## Segurança

### Rate Limiting

- **Por usuário**: Máximo 3 tokens válidos simultaneamente
- **Por IP**: Máximo 5 solicitações por hora
- **Middleware**: Rate limiting adicional nas rotas críticas

### Tokens de Segurança

- **Geração**: Tokens criptograficamente seguros (32 bytes)
- **Armazenamento**: Hash SHA-256 no banco de dados
- **Expiração**: 24 horas
- **Uso único**: Tokens são invalidados após uso

### Validações de Senha

- Mínimo 8 caracteres
- Pelo menos uma letra minúscula
- Pelo menos uma letra maiúscula
- Pelo menos um número
- Pelo menos um caractere especial
- Verificação contra senhas comuns
- Detecção de padrões sequenciais

## Configuração

### E-mail (appsettings.json)

```json
{
  "EmailSettings": {
    "Enabled": true,
    "SmtpServer": "smtp.gmail.com",
    "SmtpPort": 587,
    "EnableSsl": true,
    "UserName": "<EMAIL>",
    "Password": "your-app-password",
    "FromEmail": "<EMAIL>",
    "FromName": "Smar AssetView Nx",
    "ReplyToEmail": "<EMAIL>",
    "TimeoutSeconds": 30,
    "BaseUrl": "https://your-domain.com"
  }
}
```

### Registro de Serviços

```csharp
// Program.cs ou Startup.cs
services.AddInfrastructure(configuration);
```

## Uso

### Páginas Disponíveis

1. **`/forgot-password`** - Solicitar recuperação
2. **`/forgot-password-confirmation`** - Confirmação de envio
3. **`/reset-password?token=...`** - Redefinir senha
4. **`/login`** - Login com link para recuperação

### API Endpoints

```csharp
// Solicitar recuperação
var result = await authService.ForgotPasswordAsync(email);

// Validar token
var result = await authService.ValidateResetTokenAsync(token);

// Redefinir senha
var result = await authService.ResetPasswordAsync(new ResetPasswordRequest
{
    Email = email,
    Token = token,
    NewPassword = newPassword
});
```

## Monitoramento

### Logs de Auditoria

Todos os eventos são registrados com informações estruturadas:

```json
{
  "EventType": "PasswordResetRequest",
  "Email": "<EMAIL>",
  "IpAddress": "***********",
  "UserAgent": "Mozilla/5.0...",
  "Success": true,
  "Timestamp": "2024-12-25T10:00:00Z"
}
```

### Métricas Importantes

- Taxa de sucesso de recuperação
- Tentativas bloqueadas por rate limiting
- Tokens expirados vs. utilizados
- Distribuição geográfica de tentativas

## Manutenção

### Limpeza Automática

O `TokenCleanupService` executa a cada 6 horas para:
- Remover tokens expirados
- Limpar logs antigos
- Otimizar performance do banco

### Backup e Recuperação

- Tokens são armazenados na tabela `PasswordResetTokens`
- Logs de auditoria são mantidos por 90 dias
- Backup regular do banco de dados recomendado

## Troubleshooting

### Problemas Comuns

1. **E-mails não chegam**
   - Verificar configurações SMTP
   - Verificar pasta de spam
   - Validar credenciais de e-mail

2. **Rate limiting muito restritivo**
   - Ajustar limites no `PasswordResetTokenService`
   - Verificar configuração do middleware

3. **Tokens expiram muito rápido**
   - Ajustar `TokenExpirationHours` no serviço
   - Verificar timezone do servidor

### Logs Úteis

```bash
# Filtrar logs de recuperação
grep "PasswordReset" application.log

# Verificar rate limiting
grep "Rate limit exceeded" application.log

# Auditoria de segurança
grep "AUDIT:" application.log | jq .
```

## Desenvolvimento

### Testes

```bash
# Executar testes unitários
dotnet test tests/Infrastructure.Tests/

# Executar testes de integração
dotnet test tests/Integration.Tests/

# Cobertura de código
dotnet test --collect:"XPlat Code Coverage"
```

### Extensibilidade

O sistema foi projetado para ser extensível:

- Novos provedores de e-mail podem ser adicionados
- Políticas de senha customizáveis
- Templates de e-mail personalizáveis
- Integração com sistemas de auditoria externos

## Conformidade

### LGPD/GDPR

- Dados pessoais são minimizados
- Logs contêm apenas informações necessárias
- Tokens são automaticamente removidos
- Usuários podem solicitar remoção de dados

### Segurança

- Implementa OWASP Top 10 mitigations
- Rate limiting contra ataques de força bruta
- Tokens seguros contra timing attacks
- Logs de auditoria para compliance
