using AssetView.Nx.Modules.OpcDa.Models;

namespace AssetView.Nx.Modules.OpcDa.Interfaces;

/// <summary>
/// Interface para navegação avançada de tags OPC
/// </summary>
public interface IOpcTagNavigationService : IDisposable
{
    /// <summary>
    /// Navega pela estrutura hierárquica de tags do servidor OPC
    /// </summary>
    /// <param name="serverProgId">ProgID do servidor OPC</param>
    /// <param name="parentPath">Caminho do nó pai (null para raiz)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de nós de tags</returns>
    Task<List<OpcTagNode>> BrowseTagsAsync(string serverProgId, string? parentPath = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Busca tags por filtro de nome
    /// </summary>
    /// <param name="serverProgId">ProgID do servidor OPC</param>
    /// <param name="searchFilter">Filtro de busca</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de tags que correspondem ao filtro</returns>
    Task<List<OpcTagNode>> SearchTagsAsync(string serverProgId, string searchFilter, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém detalhes completos de um tag específico
    /// </summary>
    /// <param name="serverProgId">ProgID do servidor OPC</param>
    /// <param name="tagPath">Caminho completo do tag</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Detalhes do tag ou null se não encontrado</returns>
    Task<OpcTagDetails?> GetTagDetailsAsync(string serverProgId, string tagPath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Limpa cache de tags
    /// </summary>
    /// <returns>Task</returns>
    Task ClearCacheAsync();
}
