using Microsoft.AspNetCore.SignalR;
using AssetView.Nx.Modules.OpcDa.Interfaces;
using AssetView.Nx.Modules.OpcDa.Models;
using AssetView.Nx.Modules.OpcDa.DTOs;

namespace AssetView.Nx.Dashboard.Hubs;

/// <summary>
/// Hub SignalR para transmissão de dados OPC em tempo real
/// </summary>
public class OpcDataHub : Hub
{
    private readonly IOpcDaService _opcDaService;
    private readonly ILogger<OpcDataHub> _logger;

    public OpcDataHub(IOpcDaService opcDaService, ILogger<OpcDataHub> logger)
    {
        _opcDaService = opcDaService;
        _logger = logger;
    }

    public override async Task OnConnectedAsync()
    {
        _logger.LogInformation("Cliente conectado ao OpcDataHub: {ConnectionId}", Context.ConnectionId);
        
        // Enviar status inicial
        await SendConnectionStatus();
        await SendStatistics();
        
        await base.OnConnectedAsync();
    }

    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        _logger.LogInformation("Cliente desconectado do OpcDataHub: {ConnectionId}", Context.ConnectionId);
        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// Solicita todos os itens OPC
    /// </summary>
    public async Task RequestAllItems()
    {
        try
        {
            _logger.LogDebug("Cliente {ConnectionId} solicitou todos os itens", Context.ConnectionId);
            
            var status = _opcDaService.GetConnectionStatus();
            if (status != OpcConnectionStatus.Connected)
            {
                await Clients.Caller.SendAsync("Error", "OPC DA Service não está conectado");
                return;
            }

            // Simular dados para demonstração
            var items = GenerateSimulatedData();
            await Clients.Caller.SendAsync("AllItemsReceived", items);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao solicitar todos os itens");
            await Clients.Caller.SendAsync("Error", ex.Message);
        }
    }

    /// <summary>
    /// Solicita estatísticas do servidor OPC
    /// </summary>
    public async Task RequestStatistics()
    {
        try
        {
            _logger.LogDebug("Cliente {ConnectionId} solicitou estatísticas", Context.ConnectionId);
            await SendStatistics();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao solicitar estatísticas");
            await Clients.Caller.SendAsync("Error", ex.Message);
        }
    }

    /// <summary>
    /// Envia status de conexão para o cliente
    /// </summary>
    private async Task SendConnectionStatus()
    {
        try
        {
            var status = _opcDaService.GetConnectionStatus();
            var statusDto = new OpcConnectionStatusDto
            {
                Status = status.ToString(),
                Timestamp = DateTime.UtcNow,
                TotalItems = 0,
                ActiveItems = 0
            };

            await Clients.Caller.SendAsync("ConnectionStatusChanged", statusDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao enviar status de conexão");
        }
    }

    /// <summary>
    /// Envia estatísticas para o cliente
    /// </summary>
    private async Task SendStatistics()
    {
        try
        {
            // Simular estatísticas já que IOpcDaService não tem GetStatistics
            var statsDto = new OpcServerStatisticsDto
            {
                UptimeMs = (long)(DateTime.UtcNow - DateTime.Today).TotalMilliseconds,
                TotalUpdates = 1000,
                UpdatesPerSecond = 2.5,
                ConnectionErrors = 0,
                LastError = null,
                ActiveGroups = new List<string> { "Random", "Simulation", "System" }
            };

            await Clients.Caller.SendAsync("StatisticsReceived", statsDto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao enviar estatísticas");
        }
    }

    /// <summary>
    /// Gera dados simulados para demonstração
    /// </summary>
    private List<OpcDataUpdateDto> GenerateSimulatedData()
    {
        var random = new Random();
        var items = new List<OpcDataUpdateDto>();

        // Simular alguns tags comuns
        var tagNames = new[]
        {
            "Random.Real8",
            "Random.Int4",
            "Simulation.Ramp",
            "Simulation.Sine",
            "System.Time",
            "System.Date"
        };

        foreach (var tagName in tagNames)
        {
            items.Add(new OpcDataUpdateDto
            {
                TagName = tagName,
                Value = GenerateRandomValue(tagName, random),
                Quality = "Good",
                Timestamp = DateTime.UtcNow,
                DataType = GetDataType(tagName),
                GroupName = GetGroupName(tagName)
            });
        }

        return items;
    }

    private object GenerateRandomValue(string tagName, Random random)
    {
        return tagName switch
        {
            _ => random.NextDouble() * 100
        };
    }

    private string GetDataType(string tagName)
    {
        return tagName switch
        {
            var name when name.Contains("Real") => "Double",
            var name when name.Contains("Int") => "Integer",
            var name when name.Contains("Time") || name.Contains("Date") => "String",
            _ => "Variant"
        };
    }

    private string GetGroupName(string tagName)
    {
        return tagName switch
        {
            var name when name.StartsWith("Random") => "Random",
            var name when name.StartsWith("Simulation") => "Simulation", 
            var name when name.StartsWith("System") => "System",
            _ => "Default"
        };
    }
}
