@page "/access-denied"

<PageTitle>Acesso Negado - Smar Hart System</PageTitle>

<MudContainer MaxWidth="MaxWidth.Small" Class="d-flex flex-column justify-center align-center" Style="min-height: 100vh;">
    <MudCard Elevation="8" Class="pa-8 text-center">
        <MudCardContent>
            <MudIcon Icon="@Icons.Material.Filled.Block" 
                     Color="Color.Error" 
                     Size="Size.Large" 
                     Class="mb-4" 
                     Style="font-size: 4rem;" />
            
            <MudText Typo="Typo.h4" Color="Color.Error" Class="mb-4">
                Acesso Negado
            </MudText>
            
            <MudText Typo="Typo.body1" Class="mb-6">
                Você não tem permissão para acessar esta página. Entre em contato com o administrador do sistema se acredita que isso é um erro.
            </MudText>
            
            <MudStack Row Spacing="2" Justify="Justify.Center">
                <MudButton Variant="Variant.Filled" 
                           Color="Color.Primary" 
                           StartIcon="@Icons.Material.Filled.Home"
                           Href="/">
                    Página Inicial
                </MudButton>
                
                <MudButton Variant="Variant.Outlined" 
                           Color="Color.Secondary" 
                           StartIcon="@Icons.Material.Filled.ArrowBack"
                           OnClick="GoBack">
                    Voltar
                </MudButton>
            </MudStack>
        </MudCardContent>
    </MudCard>
</MudContainer>

@code {
    [Inject] private NavigationManager Navigation { get; set; } = null!;
    [Inject] private IJSRuntime JSRuntime { get; set; } = null!;

    private async Task GoBack()
    {
        await JSRuntime.InvokeVoidAsync("history.back");
    }
}
