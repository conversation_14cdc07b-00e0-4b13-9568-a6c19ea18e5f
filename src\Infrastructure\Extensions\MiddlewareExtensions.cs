using AssetView.Nx.Infrastructure.Middleware;
using Microsoft.AspNetCore.Builder;

namespace AssetView.Nx.Infrastructure.Extensions;

/// <summary>
/// Extensões para configuração de middlewares
/// </summary>
public static class MiddlewareExtensions
{
    /// <summary>
    /// Adiciona o middleware de rate limiting
    /// </summary>
    /// <param name="app">Application builder</param>
    /// <returns>Application builder</returns>
    public static IApplicationBuilder UseRateLimiting(this IApplicationBuilder app)
    {
        return app.UseMiddleware<RateLimitingMiddleware>();
    }
}
