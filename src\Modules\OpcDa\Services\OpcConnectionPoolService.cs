using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Diagnostics;
using Opc;
using Opc.Da;
using OpcCom;
using AssetView.Nx.Modules.OpcDa.Models;

namespace AssetView.Nx.Modules.OpcDa.Services;

/// <summary>
/// Serviço de pool de conexões OPC para reutilização eficiente de conexões
/// </summary>
public class OpcConnectionPoolService : IDisposable
{
    private readonly ILogger<OpcConnectionPoolService> _logger;
    private readonly OpcPerformanceMetricsService? _metricsService;
    private readonly OpcConnectionPoolConfig _config;
    private readonly ConcurrentDictionary<string, OpcConnectionPool> _connectionPools = new();
    private readonly System.Threading.Timer _maintenanceTimer;
    private readonly OpcCom.Factory _factory;
    private readonly object _poolLock = new();

    public OpcConnectionPoolService(
        ILogger<OpcConnectionPoolService> logger,
        IOptions<OpcConnectionPoolConfig> config)
    {
        _logger = logger;
        _metricsService = null; // Será injetado via propriedade se necessário
        _config = config.Value;
        _factory = new OpcCom.Factory();
        
        // Timer para manutenção do pool (limpeza de conexões inativas, etc.)
        _maintenanceTimer = new System.Threading.Timer(PerformMaintenance, null,
            _config.MaintenanceInterval, _config.MaintenanceInterval);
    }

    /// <summary>
    /// Obtém uma conexão do pool ou cria uma nova
    /// </summary>
    public async Task<IOpcPooledConnection?> GetConnectionAsync(string serverProgId, string host, CancellationToken cancellationToken = default)
    {
        var metrics = _metricsService != null ? (OpcOperationMeasurement)_metricsService.StartOperation("GetPooledConnection", serverProgId) : null;
        
        try
        {
            var poolKey = $"{host}:{serverProgId}";
            var pool = _connectionPools.GetOrAdd(poolKey, _ => new OpcConnectionPool(poolKey, _config, _logger));

            var connectionInfo = await pool.GetConnectionInfoAsync(host, serverProgId, _factory, cancellationToken);

            if (connectionInfo != null)
            {
                metrics?.Complete(true);
                metrics?.Dispose();
                _logger.LogDebug("Conexão obtida do pool: {PoolKey}", poolKey);
                return new OpcPooledConnectionWrapper(connectionInfo, pool, _metricsService);
            }
            else
            {
                metrics?.Complete(false, "Falha ao obter conexão do pool");
                metrics?.Dispose();
                _logger.LogWarning("Falha ao obter conexão do pool: {PoolKey}", poolKey);
                return null;
            }
        }
        catch (Exception ex)
        {
            metrics?.Complete(false, ex.Message);
            metrics?.Dispose();
            _logger.LogError(ex, "Erro ao obter conexão do pool: {ServerProgId}@{Host}", serverProgId, host);
            return null;
        }
    }

    /// <summary>
    /// Obtém estatísticas do pool de conexões
    /// </summary>
    public OpcConnectionPoolStatistics GetPoolStatistics()
    {
        lock (_poolLock)
        {
            var stats = new OpcConnectionPoolStatistics
            {
                TotalPools = _connectionPools.Count,
                GeneratedAt = DateTime.UtcNow
            };

            foreach (var pool in _connectionPools.Values)
            {
                var poolStats = pool.GetStatistics();
                stats.TotalConnections += poolStats.TotalConnections;
                stats.ActiveConnections += poolStats.ActiveConnections;
                stats.IdleConnections += poolStats.IdleConnections;
                stats.TotalConnectionsCreated += poolStats.TotalConnectionsCreated;
                stats.TotalConnectionsDestroyed += poolStats.TotalConnectionsDestroyed;
                stats.TotalConnectionRequests += poolStats.TotalConnectionRequests;
                stats.PoolHits += poolStats.PoolHits;
                stats.PoolMisses += poolStats.PoolMisses;
                
                stats.PoolDetails[pool.PoolKey] = poolStats;
            }

            stats.PoolHitRate = stats.TotalConnectionRequests > 0 ? 
                (double)stats.PoolHits / stats.TotalConnectionRequests * 100 : 0;

            return stats;
        }
    }

    /// <summary>
    /// Limpa pools inativos
    /// </summary>
    public void ClearInactivePools(TimeSpan maxIdleTime)
    {
        lock (_poolLock)
        {
            var poolsToRemove = new List<string>();
            
            foreach (var kvp in _connectionPools)
            {
                var pool = kvp.Value;
                if (pool.IsInactive(maxIdleTime))
                {
                    poolsToRemove.Add(kvp.Key);
                }
            }

            foreach (var poolKey in poolsToRemove)
            {
                if (_connectionPools.TryRemove(poolKey, out var pool))
                {
                    try
                    {
                        pool.Dispose();
                        _logger.LogInformation("Pool inativo removido: {PoolKey}", poolKey);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Erro ao remover pool inativo: {PoolKey}", poolKey);
                    }
                }
            }
        }
    }

    private void PerformMaintenance(object? state)
    {
        try
        {
            _logger.LogDebug("Iniciando manutenção do pool de conexões");

            // Limpar pools inativos
            ClearInactivePools(_config.MaxIdleTime);

            // Executar manutenção em cada pool
            foreach (var pool in _connectionPools.Values)
            {
                try
                {
                    pool.PerformMaintenance();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Erro durante manutenção do pool: {PoolKey}", pool.PoolKey);
                }
            }

            // Log de estatísticas
            var stats = GetPoolStatistics();
            _logger.LogInformation("Pool Stats - Pools: {TotalPools}, Conexões: {TotalConnections} " +
                                 "(Ativas: {ActiveConnections}, Idle: {IdleConnections}), Hit Rate: {HitRate:F1}%",
                stats.TotalPools, stats.TotalConnections, stats.ActiveConnections, 
                stats.IdleConnections, stats.PoolHitRate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro durante manutenção do pool de conexões");
        }
    }

    public void Dispose()
    {
        try
        {
            _maintenanceTimer?.Dispose();
            
            foreach (var pool in _connectionPools.Values)
            {
                try
                {
                    pool.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Erro ao descartar pool: {PoolKey}", pool.PoolKey);
                }
            }
            
            _connectionPools.Clear();
            _factory?.Dispose();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro durante dispose do OpcConnectionPoolService");
        }
    }
}

/// <summary>
/// Pool individual para um servidor OPC específico
/// </summary>
internal class OpcConnectionPool : IDisposable
{
    private readonly ConcurrentQueue<OpcPooledConnectionInfo> _availableConnections = new();
    private readonly ConcurrentDictionary<string, OpcPooledConnectionInfo> _activeConnections = new();
    private readonly OpcConnectionPoolConfig _config;
    private readonly ILogger _logger;
    private readonly object _poolLock = new();
    private long _totalConnectionsCreated = 0;
    private long _totalConnectionsDestroyed = 0;
    private long _totalConnectionRequests = 0;
    private long _poolHits = 0;
    private long _poolMisses = 0;
    private DateTime _lastActivity = DateTime.UtcNow;

    public string PoolKey { get; }

    public OpcConnectionPool(string poolKey, OpcConnectionPoolConfig config, ILogger logger)
    {
        PoolKey = poolKey;
        _config = config;
        _logger = logger;
    }

    public async Task<OpcPooledConnectionInfo?> GetConnectionInfoAsync(string host, string serverProgId, OpcCom.Factory factory, CancellationToken cancellationToken)
    {
        Interlocked.Increment(ref _totalConnectionRequests);
        _lastActivity = DateTime.UtcNow;

        // Tentar obter conexão existente do pool
        if (_availableConnections.TryDequeue(out var pooledConnection))
        {
            if (IsConnectionValid(pooledConnection))
            {
                Interlocked.Increment(ref _poolHits);
                _activeConnections.TryAdd(pooledConnection.Id, pooledConnection);
                _logger.LogDebug("Conexão reutilizada do pool: {PoolKey}", PoolKey);
                return pooledConnection;
            }
            else
            {
                // Conexão inválida, descartar
                DisposeConnection(pooledConnection);
            }
        }

        // Criar nova conexão se o pool não estiver no limite
        if (_activeConnections.Count + _availableConnections.Count < _config.MaxPoolSize)
        {
            try
            {
                var newConnection = await CreateNewConnectionAsync(host, serverProgId, factory, cancellationToken);
                if (newConnection != null)
                {
                    Interlocked.Increment(ref _poolMisses);
                    Interlocked.Increment(ref _totalConnectionsCreated);
                    _activeConnections.TryAdd(newConnection.Id, newConnection);
                    _logger.LogDebug("Nova conexão criada para o pool: {PoolKey}", PoolKey);
                    return newConnection;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Erro ao criar nova conexão para o pool: {PoolKey}", PoolKey);
            }
        }

        Interlocked.Increment(ref _poolMisses);
        _logger.LogWarning("Pool de conexões esgotado: {PoolKey}", PoolKey);
        return null;
    }

    public void ReturnConnection(string connectionId)
    {
        if (_activeConnections.TryRemove(connectionId, out var connection))
        {
            if (IsConnectionValid(connection) && _availableConnections.Count < _config.MaxPoolSize)
            {
                connection.LastUsed = DateTime.UtcNow;
                _availableConnections.Enqueue(connection);
                _logger.LogDebug("Conexão retornada ao pool: {PoolKey}", PoolKey);
            }
            else
            {
                DisposeConnection(connection);
            }
        }
    }

    public OpcPoolStatistics GetStatistics()
    {
        return new OpcPoolStatistics
        {
            PoolKey = PoolKey,
            TotalConnections = _activeConnections.Count + _availableConnections.Count,
            ActiveConnections = _activeConnections.Count,
            IdleConnections = _availableConnections.Count,
            TotalConnectionsCreated = _totalConnectionsCreated,
            TotalConnectionsDestroyed = _totalConnectionsDestroyed,
            TotalConnectionRequests = _totalConnectionRequests,
            PoolHits = _poolHits,
            PoolMisses = _poolMisses,
            LastActivity = _lastActivity
        };
    }

    public bool IsInactive(TimeSpan maxIdleTime)
    {
        return DateTime.UtcNow - _lastActivity > maxIdleTime && 
               _activeConnections.IsEmpty && _availableConnections.IsEmpty;
    }

    public void PerformMaintenance()
    {
        // Remover conexões expiradas
        var expiredConnections = new List<OpcPooledConnectionInfo>();
        var tempQueue = new ConcurrentQueue<OpcPooledConnectionInfo>();

        while (_availableConnections.TryDequeue(out var connection))
        {
            if (DateTime.UtcNow - connection.LastUsed > _config.ConnectionTimeout)
            {
                expiredConnections.Add(connection);
            }
            else
            {
                tempQueue.Enqueue(connection);
            }
        }

        // Recolocar conexões válidas na fila
        while (tempQueue.TryDequeue(out var connection))
        {
            _availableConnections.Enqueue(connection);
        }

        // Descartar conexões expiradas
        foreach (var connection in expiredConnections)
        {
            DisposeConnection(connection);
        }

        if (expiredConnections.Count > 0)
        {
            _logger.LogDebug("Removidas {Count} conexões expiradas do pool: {PoolKey}", 
                expiredConnections.Count, PoolKey);
        }
    }

    private async Task<OpcPooledConnectionInfo?> CreateNewConnectionAsync(string host, string serverProgId, OpcCom.Factory factory, CancellationToken cancellationToken)
    {
        try
        {
            var url = new Opc.URL($"opcda://{host}/{serverProgId}");
            var server = new Opc.Da.Server(factory, url);
            
            var connectData = new Opc.ConnectData(new System.Net.NetworkCredential());
            
            await Task.Run(() => server.Connect(connectData), cancellationToken);

            return new OpcPooledConnectionInfo
            {
                Id = Guid.NewGuid().ToString(),
                Server = server,
                Created = DateTime.UtcNow,
                LastUsed = DateTime.UtcNow,
                Host = host,
                ServerProgId = serverProgId
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao criar conexão OPC: {Host}:{ServerProgId}", host, serverProgId);
            return null;
        }
    }

    private bool IsConnectionValid(OpcPooledConnectionInfo connection)
    {
        try
        {
            // Verificar se a conexão ainda está ativa
            var status = connection.Server.GetStatus();
            return status != null;
        }
        catch
        {
            return false;
        }
    }

    private void DisposeConnection(OpcPooledConnectionInfo connection)
    {
        try
        {
            connection.Server?.Disconnect();
            connection.Server?.Dispose();
            Interlocked.Increment(ref _totalConnectionsDestroyed);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Erro ao descartar conexão: {ConnectionId}", connection.Id);
        }
    }

    public void Dispose()
    {
        // Descartar todas as conexões ativas
        foreach (var connection in _activeConnections.Values)
        {
            DisposeConnection(connection);
        }
        _activeConnections.Clear();

        // Descartar todas as conexões disponíveis
        while (_availableConnections.TryDequeue(out var connection))
        {
            DisposeConnection(connection);
        }
    }
}
