using AssetView.Nx.Infrastructure.Middleware;
using Microsoft.AspNetCore.Builder;

namespace AssetView.Nx.Infrastructure.Extensions;

/// <summary>
/// Extensões para configuração do pipeline da aplicação
/// </summary>
public static class ApplicationBuilderExtensions
{
    /// <summary>
    /// Adiciona o middleware de resolução de tenant
    /// </summary>
    public static IApplicationBuilder UseTenantResolution(this IApplicationBuilder app)
    {
        return app.UseMiddleware<TenantResolutionMiddleware>();
    }
}
