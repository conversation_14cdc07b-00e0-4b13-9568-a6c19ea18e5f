using Microsoft.AspNetCore.Authorization;

namespace AssetView.Nx.Dashboard.Components.Shared;

/// <summary>
/// Classe base para páginas que requerem privilégios de administrador
/// </summary>
[Authorize(Policy = "Admin")]
public abstract class AdminPageBase : ProtectedPageBase
{
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        
        // Verificação adicional de autorização
        if (!IsAdmin)
        {
            Navigation.NavigateTo("/access-denied", true);
            return;
        }
    }
}
