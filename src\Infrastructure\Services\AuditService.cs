using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace AssetView.Nx.Infrastructure.Services;

/// <summary>
/// Implementação do serviço de auditoria
/// </summary>
public class AuditService : IAuditService
{
    private readonly ILogger<AuditService> _logger;

    public AuditService(ILogger<AuditService> logger)
    {
        _logger = logger;
    }

    public async Task LogPasswordResetRequestAsync(string email, string? ipAddress, string? userAgent, bool success, string? reason = null)
    {
        var auditData = new
        {
            EventType = "PasswordResetRequest",
            Email = email,
            IpAddress = ipAddress,
            UserAgent = userAgent,
            Success = success,
            Reason = reason,
            Timestamp = DateTime.UtcNow
        };

        var message = success 
            ? "Password reset requested for {Email} from {IpAddress}"
            : "Password reset request failed for {Email} from {IpAddress}: {Reason}";

        if (success)
        {
            _logger.LogInformation(message, email, ipAddress);
        }
        else
        {
            _logger.LogWarning(message, email, ipAddress, reason);
        }

        // Log estruturado para análise posterior
        _logger.LogInformation("AUDIT: {AuditData}", JsonSerializer.Serialize(auditData));

        await Task.CompletedTask;
    }

    public async Task LogPasswordResetAsync(string userId, string email, string? ipAddress, string? userAgent, bool success, string? reason = null)
    {
        var auditData = new
        {
            EventType = "PasswordReset",
            UserId = userId,
            Email = email,
            IpAddress = ipAddress,
            UserAgent = userAgent,
            Success = success,
            Reason = reason,
            Timestamp = DateTime.UtcNow
        };

        var message = success 
            ? "Password reset completed for user {UserId} ({Email}) from {IpAddress}"
            : "Password reset failed for user {UserId} ({Email}) from {IpAddress}: {Reason}";

        if (success)
        {
            _logger.LogInformation(message, userId, email, ipAddress);
        }
        else
        {
            _logger.LogWarning(message, userId, email, ipAddress, reason);
        }

        _logger.LogInformation("AUDIT: {AuditData}", JsonSerializer.Serialize(auditData));

        await Task.CompletedTask;
    }

    public async Task LogLoginAttemptAsync(string emailOrUserName, string? ipAddress, string? userAgent, bool success, string? reason = null)
    {
        var auditData = new
        {
            EventType = "LoginAttempt",
            EmailOrUserName = emailOrUserName,
            IpAddress = ipAddress,
            UserAgent = userAgent,
            Success = success,
            Reason = reason,
            Timestamp = DateTime.UtcNow
        };

        var message = success 
            ? "Successful login for {EmailOrUserName} from {IpAddress}"
            : "Failed login attempt for {EmailOrUserName} from {IpAddress}: {Reason}";

        if (success)
        {
            _logger.LogInformation(message, emailOrUserName, ipAddress);
        }
        else
        {
            _logger.LogWarning(message, emailOrUserName, ipAddress, reason);
        }

        _logger.LogInformation("AUDIT: {AuditData}", JsonSerializer.Serialize(auditData));

        await Task.CompletedTask;
    }

    public async Task LogTokenValidationAsync(string tokenHash, string? ipAddress, string? userAgent, bool success, string? reason = null)
    {
        var auditData = new
        {
            EventType = "TokenValidation",
            TokenHash = tokenHash,
            IpAddress = ipAddress,
            UserAgent = userAgent,
            Success = success,
            Reason = reason,
            Timestamp = DateTime.UtcNow
        };

        var message = success 
            ? "Token validation successful from {IpAddress}"
            : "Token validation failed from {IpAddress}: {Reason}";

        if (success)
        {
            _logger.LogInformation(message, ipAddress);
        }
        else
        {
            _logger.LogWarning(message, ipAddress, reason);
        }

        _logger.LogInformation("AUDIT: {AuditData}", JsonSerializer.Serialize(auditData));

        await Task.CompletedTask;
    }

    public async Task LogRateLimitExceededAsync(string ipAddress, string path, string? userAgent)
    {
        var auditData = new
        {
            EventType = "RateLimitExceeded",
            IpAddress = ipAddress,
            Path = path,
            UserAgent = userAgent,
            Timestamp = DateTime.UtcNow
        };

        _logger.LogWarning("Rate limit exceeded for {IpAddress} on path {Path}", ipAddress, path);
        _logger.LogInformation("AUDIT: {AuditData}", JsonSerializer.Serialize(auditData));

        await Task.CompletedTask;
    }
}
