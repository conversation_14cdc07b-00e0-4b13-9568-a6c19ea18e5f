using System.ComponentModel.DataAnnotations;

namespace AssetView.Nx.Modules.OpcDa.Models;

/// <summary>
/// Informações de um servidor OPC disponível
/// </summary>
public class OpcServerInfo
{
    /// <summary>
    /// ProgID do servidor OPC
    /// </summary>
    [Required]
    public string ProgId { get; set; } = string.Empty;

    /// <summary>
    /// Nome amigável do servidor
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Descrição do servidor
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Versão do servidor
    /// </summary>
    public string? Version { get; set; }

    /// <summary>
    /// Fornecedor do servidor
    /// </summary>
    public string? Vendor { get; set; }

    /// <summary>
    /// Host onde o servidor está executando
    /// </summary>
    public string Host { get; set; } = "localhost";

    /// <summary>
    /// Indica se o servidor está atualmente conectado
    /// </summary>
    public bool IsConnected { get; set; }

    /// <summary>
    /// Indica se o servidor está disponível
    /// </summary>
    public bool IsAvailable { get; set; }

    /// <summary>
    /// Última vez que o servidor foi verificado
    /// </summary>
    public DateTime LastChecked { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Informações de um tag OPC disponível no servidor
/// </summary>
public class OpcTagInfo
{
    /// <summary>
    /// Nome completo do tag (ItemID)
    /// </summary>
    [Required]
    public string TagName { get; set; } = string.Empty;

    /// <summary>
    /// Nome de exibição do tag
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// Descrição do tag
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Tipo de dados do tag
    /// </summary>
    public string DataType { get; set; } = string.Empty;

    /// <summary>
    /// Direitos de acesso (Read, Write, ReadWrite)
    /// </summary>
    public OpcAccessRights AccessRights { get; set; } = OpcAccessRights.Read;

    /// <summary>
    /// Valor atual do tag (se disponível)
    /// </summary>
    public object? CurrentValue { get; set; }

    /// <summary>
    /// Qualidade atual do tag
    /// </summary>
    public OpcQuality Quality { get; set; } = OpcQuality.Bad;

    /// <summary>
    /// Timestamp da última leitura
    /// </summary>
    public DateTime? LastUpdate { get; set; }

    /// <summary>
    /// Indica se o tag está sendo monitorado
    /// </summary>
    public bool IsMonitored { get; set; }

    /// <summary>
    /// Grupo ao qual o tag pertence (se monitorado)
    /// </summary>
    public string? GroupName { get; set; }

    /// <summary>
    /// Caminho hierárquico do tag (para organização em árvore)
    /// </summary>
    public string? Path { get; set; }

    /// <summary>
    /// Indica se é um branch (pasta) ou leaf (tag final)
    /// </summary>
    public bool IsBranch { get; set; }

    /// <summary>
    /// Tags filhos (se for um branch)
    /// </summary>
    public List<OpcTagInfo> Children { get; set; } = new();
}

/// <summary>
/// Direitos de acesso de um tag OPC
/// </summary>
public enum OpcAccessRights
{
    /// <summary>
    /// Apenas leitura
    /// </summary>
    Read = 1,

    /// <summary>
    /// Apenas escrita
    /// </summary>
    Write = 2,

    /// <summary>
    /// Leitura e escrita
    /// </summary>
    ReadWrite = 3
}

/// <summary>
/// Solicitação para adicionar tag ao monitoramento
/// </summary>
public class AddTagRequest
{
    /// <summary>
    /// Nome do tag a ser adicionado
    /// </summary>
    [Required]
    public string TagName { get; set; } = string.Empty;

    /// <summary>
    /// Nome do grupo onde adicionar o tag
    /// </summary>
    [Required]
    public string GroupName { get; set; } = string.Empty;

    /// <summary>
    /// Taxa de atualização desejada (ms)
    /// </summary>
    public int UpdateRate { get; set; } = 1000;

    /// <summary>
    /// Descrição personalizada do tag
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Unidade de medida
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    /// Valor mínimo esperado
    /// </summary>
    public double? MinValue { get; set; }

    /// <summary>
    /// Valor máximo esperado
    /// </summary>
    public double? MaxValue { get; set; }
}
