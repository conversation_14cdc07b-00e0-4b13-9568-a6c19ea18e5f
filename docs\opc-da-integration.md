# Integração OPC DA - Smar AssetView Nx

Este documento descreve a implementação da integração OPC DA no Smar AssetView Nx, que permite comunicação em tempo real com servidores OPC DA através de COM Interop e exposição dos dados via SignalR para a aplicação Blazor.

## Arquitetura

A solução é composta por três componentes principais:

### 1. AssetView.Nx.Modules.OpcDa
Módulo que contém as interfaces, modelos e serviços para comunicação OPC DA.

**Principais componentes:**
- `IOpcDaService`: Interface principal para comunicação OPC
- `OpcDaService`: Implementação usando COM Interop
- `OpcDataHub`: Hub SignalR para transmissão de dados em tempo real
- Modelos: `OpcDataItem`, `OpcGroup`, `OpcServerConfig`
- DTOs: `OpcDataUpdateDto`, `OpcConnectionStatusDto`, `OpcServerStatisticsDto`

### 2. AssetView.Nx.OpcDa.Service
Serviço Windows/Console que executa o servidor OPC DA e SignalR.

**Características:**
- Executa como serviço Windows ou aplicação console
- Conecta-se ao servidor OPC DA via COM
- Expõe dados via SignalR Hub
- Suporte a reconexão automática
- Monitoramento de múltiplos grupos e tags

### 3. AssetView.Nx.Dashboard (Cliente Blazor)
Interface web para visualização dos dados OPC em tempo real.

**Páginas:**
- `/opc/dashboard`: Dashboard com visão geral dos dados OPC
- `/opc/monitor`: Monitor detalhado com tabela de todos os tags

## Configuração

### 1. Servidor OPC DA Service

Edite o arquivo `src/Services/OpcDa/appsettings.json`:

```json
{
  "OpcServer": {
    "ProgId": "Matrikon.OPC.Simulation.1",
    "ServerName": "Matrikon OPC Server for Simulation",
    "Host": "localhost",
    "ConnectionTimeout": 5000,
    "ReconnectInterval": 10000,
    "MaxReconnectAttempts": 5,
    "Groups": [
      {
        "Name": "ProcessData",
        "IsActive": true,
        "UpdateRate": 1000,
        "Items": [
          {
            "Id": "temp001",
            "TagName": "Random.Real8",
            "IsActive": true,
            "Description": "Temperatura do processo",
            "Unit": "°C",
            "MinValue": 0.0,
            "MaxValue": 100.0
          }
        ]
      }
    ]
  },
  "Urls": "http://localhost:5000"
}
```

### 2. Cliente Blazor

Edite o arquivo `src/AssetView.Nx.Dashboard/appsettings.json`:

```json
{
  "OpcService": {
    "HubUrl": "http://localhost:5000/opcHub"
  }
}
```

## Execução

### 1. Executar o Serviço OPC DA

```bash
cd src/Services/OpcDa
dotnet run
```

O serviço será executado em `http://localhost:5000` e o hub SignalR estará disponível em `/opcHub`.

### 2. Executar a Aplicação Blazor

```bash
cd src/AssetView.Nx.Dashboard
dotnet run
```

Acesse a aplicação e navegue para:
- **Dashboard OPC**: `/opc/dashboard`
- **Monitor Detalhado**: `/opc/monitor`

## Funcionalidades

### Dashboard OPC (`/opc/dashboard`)
- **Cards de Status**: Mostra status da conexão, número de tags ativos, qualidade dos dados
- **Tags Principais**: Visualização dos principais tags com valores em tempo real
- **Estatísticas do Servidor**: Tempo ativo, total de atualizações, erros de conexão
- **Alertas de Qualidade**: Notificações para tags com qualidade ruim ou incerta

### Monitor Detalhado (`/opc/monitor`)
- **Tabela Completa**: Lista todos os tags com valores, qualidade, timestamps
- **Controles de Conexão**: Conectar/desconectar do serviço OPC
- **Atualização Manual**: Botão para forçar atualização dos dados
- **Auto Refresh**: Atualização automática das estatísticas
- **Ações por Tag**: Leitura individual e escrita de valores

### Recursos Técnicos

#### SignalR Hub
- **Eventos em Tempo Real**: Recebe atualizações automáticas dos dados OPC
- **Grupos**: Suporte a inscrição em grupos específicos de tags
- **Reconexão Automática**: Reconecta automaticamente em caso de perda de conexão
- **Tratamento de Erros**: Notificações de erros via eventos

#### Serviço OPC DA
- **COM Interop**: Comunicação direta com servidores OPC DA via COM
- **Múltiplos Grupos**: Suporte a múltiplos grupos com diferentes taxas de atualização
- **Reconexão**: Tentativas automáticas de reconexão em caso de falha
- **Monitoramento**: Estatísticas detalhadas de performance e erros

## Requisitos

### Sistema Operacional
- Windows (necessário para COM Interop com OPC DA)
- .NET 9.0 Runtime

### Servidor OPC DA
- Servidor OPC DA instalado e configurado
- Para testes, recomenda-se o Matrikon OPC Server for Simulation

### Dependências
- Microsoft.AspNetCore.SignalR
- Microsoft.AspNetCore.SignalR.Client
- System.Runtime.InteropServices
- MudBlazor (para interface)

## Desenvolvimento

### Estrutura de Pastas
```
src/
├── Modules/OpcDa/                 # Módulo OPC DA
│   ├── Models/                    # Modelos de dados
│   ├── DTOs/                      # Data Transfer Objects
│   ├── Interfaces/                # Interfaces de serviços
│   ├── Services/                  # Implementações de serviços
│   ├── Com/                       # Interfaces COM
│   ├── Hubs/                      # SignalR Hubs
│   └── Extensions/                # Extensões de configuração
├── Services/OpcDa/                # Serviço OPC DA
│   ├── Program.cs                 # Ponto de entrada
│   ├── Worker.cs                  # Worker service
│   └── appsettings.json          # Configurações
└── AssetView.Nx.Dashboard/
    ├── Services/                  # Cliente SignalR
    └── Components/Pages/OPC/      # Páginas Blazor
```

### Extensibilidade
- **Novos Protocolos**: Implementar `IOpcDaService` para outros protocolos
- **Interfaces Customizadas**: Criar novos componentes Blazor para visualização
- **Alertas**: Implementar sistema de alertas baseado em regras
- **Histórico**: Adicionar armazenamento de dados históricos

## Troubleshooting

### Problemas Comuns

1. **Erro de COM**: Verificar se o servidor OPC está instalado e registrado
2. **Conexão SignalR**: Verificar se o serviço OPC está executando na porta correta
3. **Permissões**: Executar como administrador se necessário para acesso COM
4. **Firewall**: Verificar se as portas estão liberadas

### Logs
Os logs são gravados no console e Event Log (quando executado como serviço Windows).

### Monitoramento
Use as estatísticas do servidor para monitorar performance e detectar problemas.
