@page "/opc/dashboard"
@using AssetView.Nx.Dashboard.Services
@using AssetView.Nx.Modules.OpcDa.DTOs
@inject OpcSignalRClient OpcClient
@inject ISnackbar Snackbar
@implements IAsyncDisposable

<PageTitle>Dashboard OPC - Smar Hart System</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge">
    <div class="d-flex justify-space-between align-center mb-4">
        <MudText Typo="Typo.h4">Dashboard OPC DA</MudText>
        <div class="d-flex gap-2">
            <MudButton Variant="Variant.Outlined"
                      Color="Color.Primary"
                      StartIcon="@Icons.Material.Filled.Storage"
                      Href="/opc/servers">
                Servidores
            </MudButton>
            <MudButton Variant="Variant.Outlined"
                      Color="Color.Secondary"
                      StartIcon="@Icons.Material.Filled.Label"
                      Href="/opc/tags">
                Tags
            </MudButton>
            <MudButton Variant="Variant.Outlined"
                      Color="Color.Info"
                      StartIcon="@Icons.Material.Filled.Monitor"
                      Href="/opc/monitor">
                Monitor
            </MudButton>
        </div>
    </div>

    <!-- Cards de Status -->
    <MudGrid Class="mb-4">
        <MudItem xs="12" md="6" lg="3">
            <MudCard Elevation="4" Style="height: 120px;">
                <MudCardContent Class="d-flex align-center justify-center">
                    <div class="text-center">
                        <MudIcon Icon="@(_connectionStatus?.Status == "Connected" ? Icons.Material.Filled.CheckCircle : Icons.Material.Filled.Error)" 
                                Color="@(_connectionStatus?.Status == "Connected" ? Color.Success : Color.Error)" 
                                Size="Size.Large" />
                        <MudText Typo="Typo.h6" Class="mt-2">
                            @(_connectionStatus?.Status ?? "Desconhecido")
                        </MudText>
                        <MudText Typo="Typo.caption">Status da Conexão</MudText>
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" md="6" lg="3">
            <MudCard Elevation="4" Style="height: 120px;">
                <MudCardContent Class="d-flex align-center justify-center">
                    <div class="text-center">
                        <MudText Typo="Typo.h4" Color="Color.Primary">@_opcData.Count</MudText>
                        <MudText Typo="Typo.h6">Tags Ativos</MudText>
                        <MudText Typo="Typo.caption">Total de Variáveis</MudText>
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" md="6" lg="3">
            <MudCard Elevation="4" Style="height: 120px;">
                <MudCardContent Class="d-flex align-center justify-center">
                    <div class="text-center">
                        <MudText Typo="Typo.h4" Color="Color.Success">@_goodQualityCount</MudText>
                        <MudText Typo="Typo.h6">Qualidade Boa</MudText>
                        <MudText Typo="Typo.caption">Tags com Boa Qualidade</MudText>
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>

        <MudItem xs="12" md="6" lg="3">
            <MudCard Elevation="4" Style="height: 120px;">
                <MudCardContent Class="d-flex align-center justify-center">
                    <div class="text-center">
                        <MudText Typo="Typo.h4" Color="Color.Warning">
                            @(_statistics?.UpdatesPerSecond.ToString("F1") ?? "0.0")/s
                        </MudText>
                        <MudText Typo="Typo.h6">Taxa de Atualização</MudText>
                        <MudText Typo="Typo.caption">Atualizações por Segundo</MudText>
                    </div>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>

    <!-- Gráficos e Dados Principais -->
    <MudGrid>
        <!-- Tags Principais -->
        <MudItem xs="12" lg="8">
            <MudCard Elevation="2">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">Tags Principais</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    @if (_opcData.Any())
                    {
                        <MudGrid>
                            @foreach (var item in _opcData.Take(6))
                            {
                                <MudItem xs="12" md="6">
                                    <MudCard Outlined="true" Class="mb-2">
                                        <MudCardContent Class="pa-3">
                                            <div class="d-flex justify-space-between align-center">
                                                <div>
                                                    <MudText Typo="Typo.body2" Style="font-family: monospace;">
                                                        @item.TagName
                                                    </MudText>
                                                    <MudText Typo="Typo.h6" Color="Color.Primary">
                                                        @FormatValue(item.Value)
                                                    </MudText>
                                                </div>
                                                <div class="text-right">
                                                    <MudChip T="string" 
                                                            Color="@GetQualityColor(item.Quality)" 
                                                            Size="Size.Small">
                                                        @item.Quality
                                                    </MudChip>
                                                    <MudText Typo="Typo.caption" Class="mt-1">
                                                        @item.Timestamp.ToString("HH:mm:ss")
                                                    </MudText>
                                                </div>
                                            </div>
                                        </MudCardContent>
                                    </MudCard>
                                </MudItem>
                            }
                        </MudGrid>
                    }
                    else
                    {
                        <div class="text-center pa-4">
                            <MudIcon Icon="@Icons.Material.Filled.CloudOff" Size="Size.Large" Color="Color.Secondary" />
                            <MudText Typo="Typo.body1" Class="mt-2">Nenhum dado disponível</MudText>
                        </div>
                    }
                </MudCardContent>
            </MudCard>
        </MudItem>

        <!-- Estatísticas Detalhadas -->
        <MudItem xs="12" lg="4">
            <MudCard Elevation="2">
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudText Typo="Typo.h6">Estatísticas do Servidor</MudText>
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    @if (_statistics != null)
                    {
                        <div class="d-flex flex-column gap-3">
                            <div class="d-flex justify-space-between">
                                <MudText Typo="Typo.body2">Tempo Ativo:</MudText>
                                <MudText Typo="Typo.body2" Style="font-weight: bold;">
                                    @FormatUptime(_statistics.UptimeMs)
                                </MudText>
                            </div>
                            
                            <div class="d-flex justify-space-between">
                                <MudText Typo="Typo.body2">Total de Atualizações:</MudText>
                                <MudText Typo="Typo.body2" Style="font-weight: bold;">
                                    @_statistics.TotalUpdates.ToString("N0")
                                </MudText>
                            </div>
                            
                            <div class="d-flex justify-space-between">
                                <MudText Typo="Typo.body2">Erros de Conexão:</MudText>
                                <MudText Typo="Typo.body2" Style="font-weight: bold;" Color="@(_statistics.ConnectionErrors > 0 ? Color.Error : Color.Success)">
                                    @_statistics.ConnectionErrors
                                </MudText>
                            </div>
                            
                            @if (_statistics.LastError.HasValue)
                            {
                                <div class="d-flex justify-space-between">
                                    <MudText Typo="Typo.body2">Último Erro:</MudText>
                                    <MudText Typo="Typo.caption">
                                        @_statistics.LastError.Value.ToString("dd/MM HH:mm")
                                    </MudText>
                                </div>
                            }
                            
                            <MudDivider />
                            
                            <div>
                                <MudText Typo="Typo.body2" Class="mb-2">Grupos Ativos:</MudText>
                                @if (_statistics.ActiveGroups.Any())
                                {
                                    @foreach (var group in _statistics.ActiveGroups)
                                    {
                                        <MudChip T="string" Size="Size.Small" Class="ma-1">@group</MudChip>
                                    }
                                }
                                else
                                {
                                    <MudText Typo="Typo.caption" Color="Color.Secondary">Nenhum grupo ativo</MudText>
                                }
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="text-center pa-4">
                            <MudProgressCircular Indeterminate="true" Size="Size.Small" />
                            <MudText Typo="Typo.caption" Class="mt-2">Carregando estatísticas...</MudText>
                        </div>
                    }
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>

    <!-- Alertas e Notificações -->
    @if (_badQualityTags.Any())
    {
        <MudCard Elevation="2" Class="mt-4">
            <MudCardHeader>
                <CardHeaderContent>
                    <div class="d-flex align-center gap-2">
                        <MudIcon Icon="@Icons.Material.Filled.Warning" Color="Color.Warning" />
                        <MudText Typo="Typo.h6">Alertas de Qualidade</MudText>
                    </div>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudGrid>
                    @foreach (var tag in _badQualityTags)
                    {
                        <MudItem xs="12" md="6" lg="4">
                            <MudAlert Severity="@(tag.Quality == "Bad" ? Severity.Error : Severity.Warning)" 
                                     Dense="true" 
                                     Class="mb-2">
                                <strong>@tag.TagName</strong><br />
                                Qualidade: @tag.Quality<br />
                                <small>@tag.Timestamp.ToString("HH:mm:ss")</small>
                            </MudAlert>
                        </MudItem>
                    }
                </MudGrid>
            </MudCardContent>
        </MudCard>
    }
</MudContainer>

@code {
    private OpcConnectionStatusDto? _connectionStatus;
    private OpcServerStatisticsDto? _statistics;
    private List<OpcDataUpdateDto> _opcData = new();
    private Timer? _refreshTimer;

    private int _goodQualityCount => _opcData.Count(x => x.Quality == "Good");
    private List<OpcDataUpdateDto> _badQualityTags => _opcData.Where(x => x.Quality != "Good").ToList();

    protected override async Task OnInitializedAsync()
    {
        // Configurar eventos do cliente SignalR
        OpcClient.DataReceived += OnDataReceived;
        OpcClient.ConnectionStatusChanged += OnConnectionStatusChanged;
        OpcClient.StatisticsReceived += OnStatisticsReceived;
        OpcClient.AllItemsReceived += OnAllItemsReceived;

        // Tentar conectar se não estiver conectado
        if (!OpcClient.IsConnected)
        {
            try
            {
                await OpcClient.ConnectAsync();
            }
            catch (Exception ex)
            {
                Snackbar.Add($"Erro ao conectar: {ex.Message}", Severity.Error);
            }
        }

        // Solicitar dados iniciais
        if (OpcClient.IsConnected)
        {
            await OpcClient.RequestAllItemsAsync();
            await OpcClient.RequestStatisticsAsync();
        }

        // Configurar timer para refresh automático
        _refreshTimer = new Timer(async _ =>
        {
            if (OpcClient.IsConnected)
            {
                await InvokeAsync(async () =>
                {
                    await OpcClient.RequestStatisticsAsync();
                });
            }
        }, null, TimeSpan.FromSeconds(10), TimeSpan.FromSeconds(10));
    }

    private void OnDataReceived(object? sender, OpcDataUpdateDto data)
    {
        InvokeAsync(() =>
        {
            var existingItem = _opcData.FirstOrDefault(x => x.TagName == data.TagName);
            if (existingItem != null)
            {
                existingItem.Value = data.Value;
                existingItem.Quality = data.Quality;
                existingItem.Timestamp = data.Timestamp;
                existingItem.DataType = data.DataType;
            }
            else
            {
                _opcData.Add(data);
            }
            StateHasChanged();
        });
    }

    private void OnConnectionStatusChanged(object? sender, OpcConnectionStatusDto status)
    {
        InvokeAsync(() =>
        {
            _connectionStatus = status;
            StateHasChanged();
        });
    }

    private void OnStatisticsReceived(object? sender, OpcServerStatisticsDto statistics)
    {
        InvokeAsync(() =>
        {
            _statistics = statistics;
            StateHasChanged();
        });
    }

    private void OnAllItemsReceived(object? sender, List<OpcDataUpdateDto> items)
    {
        InvokeAsync(() =>
        {
            _opcData = items.ToList();
            StateHasChanged();
        });
    }

    private string FormatValue(object? value)
    {
        if (value == null) return "null";
        
        return value switch
        {
            double d => d.ToString("F2"),
            float f => f.ToString("F2"),
            decimal dec => dec.ToString("F2"),
            bool b => b ? "True" : "False",
            _ => value.ToString() ?? "null"
        };
    }

    private Color GetQualityColor(string quality)
    {
        return quality switch
        {
            "Good" => Color.Success,
            "Uncertain" => Color.Warning,
            "Bad" => Color.Error,
            _ => Color.Default
        };
    }

    private string FormatUptime(long uptimeMs)
    {
        var timespan = TimeSpan.FromMilliseconds(uptimeMs);
        if (timespan.TotalDays >= 1)
            return $"{timespan.Days}d {timespan.Hours}h";
        if (timespan.TotalHours >= 1)
            return $"{timespan.Hours}h {timespan.Minutes}m";
        return $"{timespan.Minutes}m {timespan.Seconds}s";
    }

    public ValueTask DisposeAsync()
    {
        _refreshTimer?.Dispose();

        OpcClient.DataReceived -= OnDataReceived;
        OpcClient.ConnectionStatusChanged -= OnConnectionStatusChanged;
        OpcClient.StatisticsReceived -= OnStatisticsReceived;
        OpcClient.AllItemsReceived -= OnAllItemsReceived;

        return ValueTask.CompletedTask;
    }
}
