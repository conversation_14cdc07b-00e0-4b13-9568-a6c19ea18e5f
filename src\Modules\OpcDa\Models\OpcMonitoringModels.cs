using OpcCom;
using System.Collections.Concurrent;

namespace AssetView.Nx.Modules.OpcDa.Models;

/// <summary>
/// Configuração para monitoramento OPC
/// </summary>
public class OpcMonitoringConfig
{
    /// <summary>
    /// ProgID do servidor OPC
    /// </summary>
    public string ServerProgId { get; set; } = string.Empty;

    /// <summary>
    /// Lista de caminhos de tags para monitorar
    /// </summary>
    public List<string> TagPaths { get; set; } = new();

    /// <summary>
    /// Taxa de atualização em milissegundos
    /// </summary>
    public int UpdateRateMs { get; set; } = 1000;

    /// <summary>
    /// Deadband percentual para mudanças de valor
    /// </summary>
    public float DeadbandPercent { get; set; } = 0.0f;

    /// <summary>
    /// Nome da sessão de monitoramento
    /// </summary>
    public string? SessionName { get; set; }

    /// <summary>
    /// Configurações adicionais
    /// </summary>
    public Dictionary<string, object> Settings { get; set; } = new();
}

/// <summary>
/// Sessão de monitoramento OPC
/// </summary>
public class OpcMonitoringSession
{
    /// <summary>
    /// ID único da sessão
    /// </summary>
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// ProgID do servidor OPC
    /// </summary>
    public string ServerProgId { get; set; } = string.Empty;

    /// <summary>
    /// Configuração da sessão
    /// </summary>
    public OpcMonitoringConfig Config { get; set; } = new();

    /// <summary>
    /// Servidor OPC conectado
    /// </summary>
    public OpcCom.Da.Server Server { get; set; } = null!;

    /// <summary>
    /// Subscrição OPC
    /// </summary>
    public OpcCom.Da.Subscription Subscription { get; set; } = null!;

    /// <summary>
    /// Tags monitorados na sessão
    /// </summary>
    public ConcurrentDictionary<string, OpcMonitoredTag> MonitoredTags { get; set; } = new();

    /// <summary>
    /// Indica se a sessão está ativa
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Hora de início da sessão
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// Última atividade da sessão
    /// </summary>
    public DateTime LastActivity { get; set; }

    /// <summary>
    /// Total de atualizações recebidas
    /// </summary>
    public long TotalUpdates { get; set; }
}

/// <summary>
/// Tag monitorado em uma sessão OPC
/// </summary>
public class OpcMonitoredTag
{
    /// <summary>
    /// Caminho do tag
    /// </summary>
    public string TagPath { get; set; } = string.Empty;

    /// <summary>
    /// Handle do cliente
    /// </summary>
    public object? ClientHandle { get; set; }

    /// <summary>
    /// Handle do servidor
    /// </summary>
    public object? ServerHandle { get; set; }

    /// <summary>
    /// Último valor lido
    /// </summary>
    public object? LastValue { get; set; }

    /// <summary>
    /// Qualidade do último valor
    /// </summary>
    public string? LastQuality { get; set; }

    /// <summary>
    /// Timestamp da última atualização
    /// </summary>
    public DateTime? LastUpdate { get; set; }

    /// <summary>
    /// Número de atualizações recebidas
    /// </summary>
    public long UpdateCount { get; set; }

    /// <summary>
    /// Data em que o tag foi adicionado ao monitoramento
    /// </summary>
    public DateTime AddedAt { get; set; }
}

/// <summary>
/// Informações de uma sessão de monitoramento
/// </summary>
public class OpcMonitoringSessionInfo
{
    /// <summary>
    /// ID da sessão
    /// </summary>
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// ProgID do servidor OPC
    /// </summary>
    public string ServerProgId { get; set; } = string.Empty;

    /// <summary>
    /// Número de tags monitorados
    /// </summary>
    public int TagCount { get; set; }

    /// <summary>
    /// Hora de início da sessão
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// Taxa de atualização em milissegundos
    /// </summary>
    public int UpdateRateMs { get; set; }

    /// <summary>
    /// Indica se a sessão está ativa
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Nome da sessão (se fornecido)
    /// </summary>
    public string? SessionName { get; set; }
}

/// <summary>
/// Estatísticas de monitoramento OPC
/// </summary>
public class OpcMonitoringStatistics
{
    /// <summary>
    /// ID da sessão (null para estatísticas globais)
    /// </summary>
    public string? SessionId { get; set; }

    /// <summary>
    /// Total de sessões
    /// </summary>
    public int TotalSessions { get; set; }

    /// <summary>
    /// Sessões ativas
    /// </summary>
    public int ActiveSessions { get; set; }

    /// <summary>
    /// Total de tags monitorados
    /// </summary>
    public int TotalTags { get; set; }

    /// <summary>
    /// Total de atualizações recebidas
    /// </summary>
    public long TotalUpdates { get; set; }

    /// <summary>
    /// Atualizações por segundo
    /// </summary>
    public double UpdatesPerSecond { get; set; }

    /// <summary>
    /// Tempo ativo em segundos
    /// </summary>
    public long UptimeSeconds { get; set; }

    /// <summary>
    /// Última atividade
    /// </summary>
    public DateTime? LastActivity { get; set; }

    /// <summary>
    /// Uso de memória estimado em bytes
    /// </summary>
    public long MemoryUsageBytes { get; set; }
}

/// <summary>
/// Valor de tag OPC
/// </summary>
public class OpcTagValue
{
    /// <summary>
    /// Caminho do tag
    /// </summary>
    public string TagPath { get; set; } = string.Empty;

    /// <summary>
    /// Valor do tag
    /// </summary>
    public object? Value { get; set; }

    /// <summary>
    /// Qualidade do valor
    /// </summary>
    public string Quality { get; set; } = string.Empty;

    /// <summary>
    /// Timestamp da leitura
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Tipo de dados
    /// </summary>
    public string DataType { get; set; } = string.Empty;
}

/// <summary>
/// Argumentos do evento de mudança de valor
/// </summary>
public class OpcValueChangedEventArgs : EventArgs
{
    /// <summary>
    /// ID da sessão
    /// </summary>
    public string SessionId { get; set; } = string.Empty;

    /// <summary>
    /// Caminho do tag
    /// </summary>
    public string TagPath { get; set; } = string.Empty;

    /// <summary>
    /// Novo valor
    /// </summary>
    public object? Value { get; set; }

    /// <summary>
    /// Qualidade do valor
    /// </summary>
    public string Quality { get; set; } = string.Empty;

    /// <summary>
    /// Timestamp da mudança
    /// </summary>
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// Argumentos do evento de mudança de status de conexão
/// </summary>
public class OpcConnectionStatusEventArgs : EventArgs
{
    /// <summary>
    /// ProgID do servidor OPC
    /// </summary>
    public string ServerProgId { get; set; } = string.Empty;

    /// <summary>
    /// Status da conexão
    /// </summary>
    public OpcConnectionStatus Status { get; set; }

    /// <summary>
    /// Mensagem adicional
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Timestamp do evento
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;
}


