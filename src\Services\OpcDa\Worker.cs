using AssetView.Nx.Modules.OpcDa.Interfaces;
using AssetView.Nx.Modules.OpcDa.Models;
using Microsoft.Extensions.Options;

namespace AssetView.Nx.OpcDa.Service;

public class Worker : BackgroundService
{
    private readonly ILogger<Worker> _logger;
    private readonly IOpcDaService _opcService;
    private readonly IOptions<OpcServerConfig> _opcConfig;

    public Worker(ILogger<Worker> logger, IOpcDaService opcService, IOptions<OpcServerConfig> opcConfig)
    {
        _logger = logger;
        _opcService = opcService;
        _opcConfig = opcConfig;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("=== Iniciando serviço OPC DA Worker ===");

        try
        {
            // Conectar ao servidor OPC
            var config = _opcConfig.Value;
            _logger.LogInformation("Configuração carregada - ProgId: {ProgId}, Host: {Host}, Grupos: {GroupCount}",
                config.ProgId, config.Host, config.Groups?.Count ?? 0);

            if (string.IsNullOrEmpty(config.ProgId))
            {
                _logger.LogError("ProgId do servidor OPC não configurado");
                return;
            }

            _logger.LogInformation("Tentando conectar ao servidor OPC: {ProgId}", config.ProgId);
            var connected = await _opcService.ConnectAsync(config, stoppingToken);
            if (!connected)
            {
                _logger.LogError("Falha ao conectar ao servidor OPC: {ProgId}", config.ProgId);
                return;
            }

            _logger.LogInformation("Conexão OPC estabelecida com sucesso");

            // Iniciar monitoramento
            _logger.LogInformation("Iniciando monitoramento de dados OPC");
            await _opcService.StartMonitoringAsync(stoppingToken);

            // Loop principal do worker
            _logger.LogInformation("Iniciando loop principal de monitoramento");
            var loopCount = 0;
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    loopCount++;

                    // Verificar status da conexão
                    var status = _opcService.GetConnectionStatus();

                    // Log detalhado a cada 12 iterações (1 minuto se delay for 5s)
                    if (loopCount % 12 == 0)
                    {
                        _logger.LogInformation("Loop #{LoopCount} - Status da conexão: {Status}", loopCount, status);

                        // Obter estatísticas
                        try
                        {
                            var stats = await _opcService.GetStatisticsAsync();
                            _logger.LogInformation("Estatísticas - Uptime: {UptimeMs}ms, Updates: {TotalUpdates}, Rate: {UpdatesPerSecond:F2}/s, Errors: {ConnectionErrors}",
                                stats.UptimeMs, stats.TotalUpdates, stats.UpdatesPerSecond, stats.ConnectionErrors);
                        }
                        catch (Exception statsEx)
                        {
                            _logger.LogWarning(statsEx, "Erro ao obter estatísticas");
                        }
                    }

                    if (status != OpcConnectionStatus.Connected)
                    {
                        _logger.LogWarning("Conexão OPC perdida. Status: {Status} - Tentando reconectar...", status);

                        // Tentar reconectar
                        var reconnected = await _opcService.ConnectAsync(config, stoppingToken);
                        if (reconnected)
                        {
                            _logger.LogInformation("Reconexão OPC bem-sucedida");
                        }
                        else
                        {
                            _logger.LogError("Falha na reconexão OPC");
                        }
                    }

                    // Aguardar antes da próxima verificação
                    await Task.Delay(5000, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("Cancelamento solicitado - saindo do loop principal");
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erro no loop principal do worker (iteração #{LoopCount})", loopCount);
                    await Task.Delay(10000, stoppingToken); // Aguardar mais tempo em caso de erro
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro fatal no serviço OPC DA Worker");
        }
        finally
        {
            try
            {
                _logger.LogInformation("Finalizando serviço OPC DA Worker...");
                await _opcService.StopMonitoringAsync(stoppingToken);
                await _opcService.DisconnectAsync(stoppingToken);
                _logger.LogInformation("=== Serviço OPC DA Worker finalizado com sucesso ===");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao finalizar serviço OPC DA Worker");
            }
        }
    }
}
