using Microsoft.AspNetCore.Components.Authorization;
using System.Security.Claims;
using AssetView.Nx.Core.Interfaces;
using AssetView.Nx.Core.Entities;
using AssetView.Nx.Modules.Users.Services;
using Microsoft.Extensions.Logging;

namespace AssetView.Nx.Dashboard.Services;

/// <summary>
/// Authentication State Provider customizado para Blazor Server
/// Gerencia o estado de autenticação usando apenas sessão em memória
/// </summary>
public class CustomAuthenticationStateProvider : AuthenticationStateProvider
{
    private readonly ITenantRepository<ApplicationUser> _userRepository;
    private readonly IUserManagementService _userService;
    private readonly ILogger<CustomAuthenticationStateProvider> _logger;
    private ClaimsPrincipal _currentUser = new(new ClaimsIdentity());
    private UserSession? _temporarySession; // Para armazenar temporariamente durante prerendering

    public CustomAuthenticationStateProvider(
        ITenantRepository<ApplicationUser> userRepository,
        IUserManagementService userService,
        ILogger<CustomAuthenticationStateProvider> logger)
    {
        _userRepository = userRepository;
        _userService = userService;
        _logger = logger;
    }

    public override Task<AuthenticationState> GetAuthenticationStateAsync()
    {
        // Usar apenas a sessão temporária em memória (sem ProtectedSessionStorage)
        if (_temporarySession != null && _temporarySession.ExpiresAt > DateTime.UtcNow)
        {
            var claims = CreateClaimsFromSession(_temporarySession);
            var identity = new ClaimsIdentity(claims, "CustomAuth");
            _currentUser = new ClaimsPrincipal(identity);
            return Task.FromResult(new AuthenticationState(_currentUser));
        }

        // Usuário não autenticado
        _currentUser = new ClaimsPrincipal(new ClaimsIdentity());
        return Task.FromResult(new AuthenticationState(_currentUser));
    }

    private static List<Claim> CreateClaimsFromSession(UserSession userSession)
    {
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, userSession.UserId),
            new(ClaimTypes.Name, userSession.FullName),
            new(ClaimTypes.Email, userSession.Email),
            new("TenantId", userSession.TenantId)
        };

        // Adicionar roles
        foreach (var role in userSession.Roles)
        {
            claims.Add(new Claim(ClaimTypes.Role, role));
        }

        return claims;
    }

    public async Task<AuthResult> LoginAsync(string emailOrUserName, string password, bool rememberMe = false)
    {
        try
        {
            _logger.LogInformation("Tentativa de login para: {EmailOrUserName}", emailOrUserName);

            // Buscar usuário por email ou username
            var user = await _userRepository.GetFirstOrDefaultAsync(u => 
                u.Email == emailOrUserName || u.UserName == emailOrUserName);

            if (user == null)
            {
                _logger.LogWarning("Usuário não encontrado: {EmailOrUserName}", emailOrUserName);
                return new AuthResult { Success = false, Message = "Credenciais inválidas" };
            }

            if (!user.IsActive)
            {
                _logger.LogWarning("Usuário inativo: {EmailOrUserName}", emailOrUserName);
                return new AuthResult { Success = false, Message = "Usuário inativo" };
            }

            // Verificar senha
            if (!VerifyPassword(password, user.PasswordHash))
            {
                _logger.LogWarning("Senha inválida para usuário: {EmailOrUserName}", emailOrUserName);
                return new AuthResult { Success = false, Message = "Credenciais inválidas" };
            }

            // Buscar roles do usuário
            var userRoles = await _userService.GetUserRolesAsync(user.Id);
            var roleNames = userRoles.Where(r => r.IsActive).Select(r => r.Name).ToList();

            // Criar sessão do usuário
            var userSession = new UserSession
            {
                UserId = user.Id,
                Email = user.Email,
                FullName = user.FullName,
                TenantId = user.TenantId,
                Roles = roleNames,
                ExpiresAt = rememberMe ? DateTime.UtcNow.AddDays(30) : DateTime.UtcNow.AddHours(8)
            };

            // Usar apenas sessão temporária em memória (sem ProtectedSessionStorage)
            _temporarySession = userSession;

            // Atualizar último login
            user.LastLoginAt = DateTime.UtcNow;
            await _userRepository.UpdateAsync(user);

            // Criar claims para o estado atual
            var claims = CreateClaimsFromSession(userSession);
            var identity = new ClaimsIdentity(claims, "CustomAuth");
            _currentUser = new ClaimsPrincipal(identity);

            // Notificar mudança de estado
            NotifyAuthenticationStateChanged(Task.FromResult(new AuthenticationState(_currentUser)));

            _logger.LogInformation("Usuário {Email} fez login com sucesso", user.Email);

            return new AuthResult { Success = true, Message = "Login realizado com sucesso" };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao fazer login do usuário {EmailOrUserName}", emailOrUserName);
            return new AuthResult { Success = false, Message = "Erro interno do servidor" };
        }
    }

    public Task<AuthResult> LogoutAsync()
    {
        try
        {
            // Limpar apenas a sessão temporária em memória
            _temporarySession = null;

            // Resetar usuário atual
            _currentUser = new ClaimsPrincipal(new ClaimsIdentity());

            // Notificar mudança de estado
            NotifyAuthenticationStateChanged(Task.FromResult(new AuthenticationState(_currentUser)));

            return Task.FromResult(new AuthResult { Success = true, Message = "Logout realizado com sucesso" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao fazer logout");
            return Task.FromResult(new AuthResult { Success = false, Message = "Erro interno do servidor" });
        }
    }

    private static bool VerifyPassword(string password, string hashedPassword)
    {
        var hashedInput = HashPassword(password);
        return hashedInput == hashedPassword;
    }

    private static string HashPassword(string password)
    {
        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var hashedBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(password + "SALT"));
        return Convert.ToBase64String(hashedBytes);
    }
}

/// <summary>
/// Classe para armazenar dados da sessão do usuário
/// </summary>
public class UserSession
{
    public string UserId { get; set; } = "";
    public string Email { get; set; } = "";
    public string FullName { get; set; } = "";
    public string TenantId { get; set; } = "";
    public List<string> Roles { get; set; } = new();
    public DateTime ExpiresAt { get; set; }
}

/// <summary>
/// Resultado de operações de autenticação
/// </summary>
public class AuthResult
{
    public bool Success { get; set; }
    public string? Message { get; set; }
}
