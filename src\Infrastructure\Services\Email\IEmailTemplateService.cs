using AssetView.Nx.Infrastructure.Services.Email.Models;

namespace AssetView.Nx.Infrastructure.Services.Email;

/// <summary>
/// Interface para serviço de templates de e-mail
/// </summary>
public interface IEmailTemplateService
{
    /// <summary>
    /// Renderiza um template de e-mail
    /// </summary>
    /// <param name="templateName">Nome do template</param>
    /// <param name="data">Dados para o template</param>
    /// <returns>Resultado da renderização</returns>
    Task<TemplateResult> RenderTemplateAsync(string templateName, object data);

    /// <summary>
    /// Verifica se um template existe
    /// </summary>
    /// <param name="templateName">Nome do template</param>
    /// <returns>True se o template existe</returns>
    Task<bool> TemplateExistsAsync(string templateName);

    /// <summary>
    /// Lista todos os templates disponíveis
    /// </summary>
    /// <returns>Lista de nomes de templates</returns>
    Task<IEnumerable<string>> GetAvailableTemplatesAsync();
}
