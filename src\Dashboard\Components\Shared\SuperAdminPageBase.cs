using Microsoft.AspNetCore.Authorization;

namespace AssetView.Nx.Dashboard.Components.Shared;

/// <summary>
/// Classe base para páginas que requerem privilégios de super administrador
/// </summary>
[Authorize(Policy = "SuperAdmin")]
public abstract class SuperAdminPageBase : ProtectedPageBase
{
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        
        // Verificação adicional de autorização
        if (!IsSuperAdmin)
        {
            Navigation.NavigateTo("/access-denied", true);
            return;
        }
    }
}
