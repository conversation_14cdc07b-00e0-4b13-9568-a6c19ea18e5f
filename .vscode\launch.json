{"version": "0.2.0", "configurations": [{"name": "Launch AssetView.Nx.Dashboard", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/src/AssetView.Nx.Dashboard/bin/Debug/net9.0-windows/AssetView.Nx.Dashboard.dll", "args": [], "cwd": "${workspaceFolder}/src/AssetView.Nx.Dashboard", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)", "uriFormat": "%s"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "sourceFileMap": {"/Views": "${workspaceFolder}/Views"}}]}