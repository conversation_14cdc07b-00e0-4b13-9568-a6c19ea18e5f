using Microsoft.Extensions.Logging;
using AssetView.Nx.Modules.OpcDa.Interfaces;
using AssetView.Nx.Modules.OpcDa.Models;
using Opc;
using Opc.Da;
using OpcCom;
using System.Collections.Concurrent;
using System.Runtime.InteropServices;

namespace AssetView.Nx.Modules.OpcDa.Services;

/// <summary>
/// Serviço especializado para browsing real de tags OPC usando IOPCBrowseServerAddressSpace
/// </summary>
public class OpcRealBrowsingService : IDisposable
{
    private readonly ILogger<OpcRealBrowsingService> _logger;
    private readonly IOpcDaService _opcDaService;
    private readonly OpcCom.Factory _factory;
    private readonly ConcurrentDictionary<string, Opc.Da.Server> _serverConnections = new();
    private readonly ConcurrentDictionary<string, List<OpcTagNode>> _browseCache = new();
    private readonly object _browseLock = new();

    public OpcRealBrowsingService(ILogger<OpcRealBrowsingService> logger, IOpcDaService opcDaService)
    {
        _logger = logger;
        _opcDaService = opcDaService;
        _factory = new OpcCom.Factory();
    }

    /// <summary>
    /// Navega pela estrutura real de tags do servidor OPC
    /// </summary>
    public async Task<List<OpcTagNode>> BrowseRealTagsAsync(string serverProgId, string host = "localhost", string? parentPath = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("=== INICIANDO BROWSING REAL DE TAGS ===");
            _logger.LogInformation("Servidor: {ProgId}@{Host}", serverProgId, host);
            _logger.LogInformation("Caminho: {Path}", parentPath ?? "root");
            _logger.LogInformation("Status do OpcDaService: {Status}", _opcDaService.GetConnectionStatus());

            // Verificar cache primeiro
            var cacheKey = $"{host}:{serverProgId}:{parentPath ?? "root"}";
            if (_browseCache.TryGetValue(cacheKey, out var cachedTags))
            {
                _logger.LogDebug("Tags encontrados no cache para: {CacheKey}", cacheKey);
                return cachedTags;
            }

            // PRIORIDADE 1: Tentar browsing direto usando conexão própria
            _logger.LogInformation("Tentando browsing direto do servidor OPC...");
            try
            {
                var directTags = await BrowseDirectlyAsync(serverProgId, host, parentPath, cancellationToken);
                if (directTags.Any())
                {
                    _logger.LogInformation("✅ Browsing direto bem-sucedido: {Count} tags encontrados", directTags.Count);

                    // Adicionar ao cache
                    _browseCache.TryAdd(cacheKey, directTags);

                    return directTags;
                }
                else
                {
                    _logger.LogWarning("⚠️ Browsing direto retornou 0 tags");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "❌ Browsing direto falhou");
            }

            // PRIORIDADE 2: Se OpcDaService estiver conectado, tentar usar sua conexão
            var connectionStatus = _opcDaService.GetConnectionStatus();
            if (connectionStatus == OpcConnectionStatus.Connected)
            {
                _logger.LogInformation("Tentando usar conexão do OpcDaService...");
                try
                {
                    // Aqui poderia implementar browsing usando a conexão do OpcDaService
                    // Por enquanto, vamos tentar novamente o browsing direto
                    var serviceTags = await BrowseDirectlyAsync(serverProgId, host, parentPath, cancellationToken);
                    if (serviceTags.Any())
                    {
                        _logger.LogInformation("✅ Browsing via OpcDaService bem-sucedido: {Count} tags encontrados", serviceTags.Count);

                        // Adicionar ao cache
                        _browseCache.TryAdd(cacheKey, serviceTags);

                        return serviceTags;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "❌ Browsing via OpcDaService falhou");
                }
            }

            // ÚLTIMO RECURSO: Usar dados simulados apenas se tudo falhar
            _logger.LogWarning("⚠️ Todos os métodos de browsing real falharam, usando dados simulados como último recurso");
            var simulatedTags = await GetSimulatedBrowsingData(parentPath);

            // Não adicionar dados simulados ao cache para forçar nova tentativa na próxima vez
            return simulatedTags;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao executar browsing real: {ProgId}@{Host}", serverProgId, host);
            return new List<OpcTagNode>();
        }
    }

    /// <summary>
    /// Tenta fazer browsing direto usando conexão própria
    /// </summary>
    private async Task<List<OpcTagNode>> BrowseDirectlyAsync(string serverProgId, string host, string? parentPath, CancellationToken cancellationToken)
    {
        return await Task.Run(() =>
        {
            var nodes = new List<OpcTagNode>();

            try
            {
                _logger.LogDebug("🔍 Criando conexão direta para browsing: {ProgId}@{Host}", serverProgId, host);

                var server = GetOrCreateServerConnection(serverProgId, host);
                if (server == null)
                {
                    _logger.LogWarning("❌ Não foi possível criar conexão direta para browsing");
                    return nodes;
                }

                _logger.LogDebug("✅ Conexão criada com sucesso, iniciando browsing...");

                var elements = BrowseServerAddressSpace(server, parentPath);

                _logger.LogDebug("📋 Browsing retornou {Count} elementos brutos", elements?.Length ?? 0);

                if (elements != null && elements.Length > 0)
                {
                    foreach (var element in elements)
                    {
                        try
                        {
                            var node = CreateTagNodeFromBrowseElement(element, parentPath);
                            if (node != null)
                            {
                                nodes.Add(node);
                                _logger.LogTrace("✅ Tag processado: {Name} (Tipo: {Type})", node.Name, node.IsBranch ? "Branch" : "Item");
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "⚠️ Erro ao processar elemento: {ElementName}", element.Name ?? "Unknown");
                        }
                    }
                }

                _logger.LogInformation("📊 Browsing direto concluído: {Count} tags válidos encontrados no caminho: {Path}",
                    nodes.Count, parentPath ?? "root");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Erro no browsing direto: {ProgId}@{Host}", serverProgId, host);
            }

            return nodes;
        }, cancellationToken);
    }

    /// <summary>
    /// Retorna dados simulados para demonstração do browsing hierárquico
    /// </summary>
    private async Task<List<OpcTagNode>> GetSimulatedBrowsingData(string? parentPath)
    {
        await Task.Delay(300); // Simular delay de rede

        var nodes = new List<OpcTagNode>();

        /*if (string.IsNullOrEmpty(parentPath))
        {
            // Nível raiz - mostrar grupos principais
            nodes.AddRange(new[]
            {
                new OpcTagNode
                {
                    ItemId = "Random",
                    Name = "Random",
                    IsBranch = true,
                    IsLeaf = false,
                    Description = "Valores aleatórios",
                    ParentPath = "",
                    DataType = "Group",
                    AccessRights = OpcAccessRights.Read
                },
                new OpcTagNode
                {
                    ItemId = "Simulation",
                    Name = "Simulation",
                    IsBranch = true,
                    IsLeaf = false,
                    Description = "Dados de simulação",
                    ParentPath = "",
                    DataType = "Group",
                    AccessRights = OpcAccessRights.Read
                },
                new OpcTagNode
                {
                    ItemId = "System",
                    Name = "System",
                    IsBranch = true,
                    IsLeaf = false,
                    Description = "Informações do sistema",
                    ParentPath = "",
                    DataType = "Group",
                    AccessRights = OpcAccessRights.Read
                }
            });
        }
        else if (parentPath == "Random")
        {
            // Subgrupo Random
            nodes.AddRange(new[]
            {
                new OpcTagNode
                {
                    ItemId = "Random.Real8",
                    Name = "Real8",
                    IsBranch = false,
                    IsLeaf = true,
                    DataType = "Double",
                    AccessRights = OpcAccessRights.Read,
                    Description = "Número real aleatório",
                    ParentPath = "Random"
                },
                new OpcTagNode
                {
                    ItemId = "Random.Int4",
                    Name = "Int4",
                    IsBranch = false,
                    IsLeaf = true,
                    DataType = "Integer",
                    AccessRights = OpcAccessRights.Read,
                    Description = "Número inteiro aleatório",
                    ParentPath = "Random"
                },
                new OpcTagNode
                {
                    ItemId = "Random.Boolean",
                    Name = "Boolean",
                    IsBranch = false,
                    IsLeaf = true,
                    DataType = "Boolean",
                    AccessRights = OpcAccessRights.Read,
                    Description = "Valor booleano aleatório",
                    ParentPath = "Random"
                }
            });
        }
        else if (parentPath == "Simulation")
        {
            // Subgrupo Simulation
            nodes.AddRange(new[]
            {
                new OpcTagNode
                {
                    ItemId = "Simulation.Temperature",
                    Name = "Temperature",
                    IsBranch = false,
                    IsLeaf = true,
                    DataType = "Double",
                    AccessRights = OpcAccessRights.ReadWrite,
                    Description = "Temperatura simulada (°C)",
                    ParentPath = "Simulation"
                },
                new OpcTagNode
                {
                    ItemId = "Simulation.Pressure",
                    Name = "Pressure",
                    IsBranch = false,
                    IsLeaf = true,
                    DataType = "Double",
                    AccessRights = OpcAccessRights.ReadWrite,
                    Description = "Pressão simulada (bar)",
                    ParentPath = "Simulation"
                },
                new OpcTagNode
                {
                    ItemId = "Simulation.Flow",
                    Name = "Flow",
                    IsBranch = false,
                    IsLeaf = true,
                    DataType = "Double",
                    AccessRights = OpcAccessRights.ReadWrite,
                    Description = "Fluxo simulado (L/min)",
                    ParentPath = "Simulation"
                }
            });
        }
        else if (parentPath == "System")
        {
            // Subgrupo System
            nodes.AddRange(new[]
            {
                new OpcTagNode
                {
                    ItemId = "System.Time",
                    Name = "Time",
                    IsBranch = false,
                    IsLeaf = true,
                    DataType = "DateTime",
                    AccessRights = OpcAccessRights.Read,
                    Description = "Hora do sistema",
                    ParentPath = "System"
                },
                new OpcTagNode
                {
                    ItemId = "System.Status",
                    Name = "Status",
                    IsBranch = false,
                    IsLeaf = true,
                    DataType = "String",
                    AccessRights = OpcAccessRights.Read,
                    Description = "Status do sistema",
                    ParentPath = "System"
                },
                new OpcTagNode
                {
                    ItemId = "System.Uptime",
                    Name = "Uptime",
                    IsBranch = false,
                    IsLeaf = true,
                    DataType = "Integer",
                    AccessRights = OpcAccessRights.Read,
                    Description = "Tempo de atividade (segundos)",
                    ParentPath = "System"
                }
            });
        }

        _logger.LogDebug("Browsing simulado retornou {Count} nós para caminho: {Path}",
            nodes.Count, parentPath ?? "root");*/

        return nodes;
    }

    /// <summary>
    /// Obtém propriedades detalhadas de um tag específico
    /// </summary>
    public async Task<OpcTagDetails?> GetRealTagPropertiesAsync(string serverProgId, string host, string itemId, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Obtendo propriedades reais do tag: {ItemId} no servidor: {ProgId}@{Host}", itemId, serverProgId, host);

            return await Task.Run(() =>
            {
                lock (_browseLock)
                {
                    var server = GetOrCreateServerConnection(serverProgId, host);
                    if (server == null)
                    {
                        return null;
                    }

                    try
                    {
                        // Obter propriedades do item
                        var items = new Opc.ItemIdentifier[] { new Opc.ItemIdentifier(itemId) };
                        var properties = server.GetProperties(items, null, false);

                        if (properties != null && properties.Length > 0)
                        {
                            var itemProperties = properties[0];
                            return CreateTagDetailsFromProperties(itemId, itemProperties);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Erro ao obter propriedades do tag: {ItemId}", itemId);
                    }

                    return null;
                }
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter propriedades do tag: {ItemId}", itemId);
            return null;
        }
    }

    /// <summary>
    /// Busca tags por filtro usando browsing real
    /// </summary>
    public async Task<List<OpcTagNode>> SearchRealTagsAsync(string serverProgId, string host, string searchPattern, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Buscando tags reais com padrão: {Pattern} no servidor: {ProgId}@{Host}", searchPattern, serverProgId, host);

            var allTags = new List<OpcTagNode>();
            
            // Buscar recursivamente em toda a estrutura
            await SearchTagsRecursive(serverProgId, host, null, searchPattern, allTags, cancellationToken);

            var filteredTags = allTags
                .Where(tag => tag.Name.Contains(searchPattern, StringComparison.OrdinalIgnoreCase) ||
                             (tag.Description?.Contains(searchPattern, StringComparison.OrdinalIgnoreCase) == true))
                .ToList();

            _logger.LogInformation("Busca real concluída: {Count} tags encontrados com padrão: {Pattern}", filteredTags.Count, searchPattern);
            return filteredTags;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar tags reais: {Pattern}", searchPattern);
            return new List<OpcTagNode>();
        }
    }

    private Opc.Da.Server? GetOrCreateServerConnection(string serverProgId, string host)
    {
        var connectionKey = $"{host}:{serverProgId}";

        _logger.LogDebug("🔗 Verificando conexão existente para: {Key}", connectionKey);

        if (_serverConnections.TryGetValue(connectionKey, out var existingServer))
        {
            try
            {
                // Verificar se a conexão ainda está ativa
                _logger.LogDebug("🔍 Testando conexão existente...");
                var status = existingServer.GetStatus();
                if (status != null)
                {
                    _logger.LogDebug("✅ Conexão existente válida, reutilizando");
                    return existingServer;
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "❌ Conexão existente inválida, removendo do cache");
                // Conexão perdida, remover do cache
                _serverConnections.TryRemove(connectionKey, out _);
                try
                {
                    existingServer?.Dispose();
                }
                catch { }
            }
        }

        try
        {
            _logger.LogInformation("🚀 Criando nova conexão OPC para: {ProgId}@{Host}", serverProgId, host);

            // Criar nova conexão
            var url = new Opc.URL($"opcda://{host}/{serverProgId}");
            _logger.LogDebug("📍 URL do servidor: {Url}", url.ToString());

            var server = new Opc.Da.Server(_factory, url);

            var connectData = new Opc.ConnectData(new System.Net.NetworkCredential());
            _logger.LogDebug("🔌 Conectando ao servidor...");

            server.Connect(connectData);

            // Verificar se a conexão foi bem-sucedida
            var serverStatus = server.GetStatus();
            _logger.LogInformation("✅ Conexão estabelecida com sucesso!");
            _logger.LogDebug("📊 Status do servidor: {Status}", serverStatus?.StatusInfo ?? "Unknown");
            _logger.LogDebug("🏷️ Vendor: {Vendor}", serverStatus?.VendorInfo ?? "Unknown");
            _logger.LogDebug("📅 Start Time: {StartTime}", serverStatus?.StartTime);

            _serverConnections.TryAdd(connectionKey, server);

            return server;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Falha ao conectar para browsing: {ProgId}@{Host}", serverProgId, host);
            _logger.LogError("💡 Verifique se o servidor OPC está rodando e se o ProgId está correto");
            return null;
        }
    }

    private Opc.Da.BrowseElement[] BrowseServerAddressSpace(Opc.Da.Server server, string? parentPath)
    {
        try
        {
            _logger.LogDebug("🔍 Configurando filtros de browsing para caminho: {Path}", parentPath ?? "root");

            // Configurar filtros de browsing para capturar todos os elementos
            var filters = new Opc.Da.BrowseFilters
            {
                BrowseFilter = Opc.Da.browseFilter.all, // Buscar branches e items
                ElementNameFilter = "", // Sem filtro de nome (buscar todos)
                VendorFilter = "", // Sem filtro de vendor
                ReturnAllProperties = true, // Retornar todas as propriedades
                ReturnPropertyValues = true, // Retornar valores das propriedades
                PropertyIDs = null // Todas as propriedades
            };

            // Executar browsing
            Opc.ItemIdentifier? itemId = null;
            if (!string.IsNullOrEmpty(parentPath))
            {
                itemId = new Opc.ItemIdentifier(parentPath);
                _logger.LogDebug("📂 Browsing no caminho específico: {Path}", parentPath);
            }
            else
            {
                _logger.LogDebug("🏠 Browsing no nível raiz");
            }

            _logger.LogDebug("🚀 Executando server.Browse()...");
            var elements = server.Browse(itemId, filters, out var moreElements);

            _logger.LogInformation("📋 Browsing executado: {Count} elementos encontrados no caminho: {Path} (MoreElements: {More})",
                elements?.Length ?? 0, parentPath ?? "root", moreElements);

            if (elements != null && elements.Length > 0)
            {
                _logger.LogDebug("📝 Elementos encontrados:");
                for (int i = 0; i < Math.Min(elements.Length, 10); i++) // Log apenas os primeiros 10
                {
                    var element = elements[i];
                    _logger.LogDebug("  [{Index}] Nome: {Name}, Tipo: {Type}, ItemId: {ItemId}",
                        i, element.Name, element.IsItem ? "Item" : "Branch", element.ItemName);
                }

                if (elements.Length > 10)
                {
                    _logger.LogDebug("  ... e mais {Count} elementos", elements.Length - 10);
                }
            }
            else
            {
                _logger.LogWarning("⚠️ Nenhum elemento encontrado no browsing");
            }

            return elements ?? new Opc.Da.BrowseElement[0];
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Erro durante browsing do address space: {Path}", parentPath ?? "root");
            return new Opc.Da.BrowseElement[0];
        }
    }

    private OpcTagNode? CreateTagNodeFromBrowseElement(Opc.Da.BrowseElement element, string? parentPath)
    {
        try
        {
            var fullPath = string.IsNullOrEmpty(parentPath) ? element.Name : $"{parentPath}.{element.Name}";

            return new OpcTagNode
            {
                ItemId = element.ItemName ?? fullPath,
                Name = element.Name,
                FullPath = fullPath,
                Description = element.Name, // OPC DA não tem descrição separada por padrão
                IsLeaf = !element.HasChildren,
                IsBranch = element.HasChildren,
                DataType = element.ItemName != null ? "Unknown" : null, // Será determinado nas propriedades
                AccessRights = element.ItemName != null ? OpcAccessRights.Read : OpcAccessRights.Read,
                ParentPath = parentPath,
                Level = (parentPath?.Split('.').Length ?? 0) + 1
            };
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Erro ao criar nó de tag para elemento: {Name}", element.Name);
            return null;
        }
    }

    private OpcTagDetails CreateTagDetailsFromProperties(string itemId, Opc.Da.ItemPropertyCollection properties)
    {
        var details = new OpcTagDetails
        {
            ItemId = itemId,
            Name = itemId,
            Properties = new Dictionary<string, object>(),
            DataType = "Unknown",
            Quality = "Unknown",
            AccessRights = "Read",
            Description = itemId
        };

        try
        {
            // Implementação simplificada - as propriedades específicas do OpcNetApi podem variar
            // TODO: Implementar mapeamento correto das propriedades quando a API estiver disponível
            _logger.LogDebug("Propriedades obtidas para tag: {ItemId}", itemId);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Erro ao processar propriedades do tag: {ItemId}", itemId);
        }

        return details;
    }

    private async Task SearchTagsRecursive(string serverProgId, string host, string? currentPath, string filter, 
        List<OpcTagNode> results, CancellationToken cancellationToken)
    {
        var tags = await BrowseRealTagsAsync(serverProgId, host, currentPath, cancellationToken);
        
        foreach (var tag in tags)
        {
            if (tag.Name.Contains(filter, StringComparison.OrdinalIgnoreCase))
            {
                results.Add(tag);
            }

            // Se é um branch, buscar recursivamente
            if (tag.IsBranch && results.Count < 1000) // Limitar para evitar sobrecarga
            {
                await SearchTagsRecursive(serverProgId, host, tag.FullPath, filter, results, cancellationToken);
            }
        }
    }

    /// <summary>
    /// Limpa cache de browsing e conexões para forçar nova tentativa
    /// </summary>
    public void ClearBrowseCache()
    {
        _browseCache.Clear();

        // Também limpar conexões para forçar reconexão
        foreach (var connection in _serverConnections.Values)
        {
            try
            {
                connection?.Disconnect();
                connection?.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Erro ao desconectar servidor durante limpeza de cache");
            }
        }
        _serverConnections.Clear();

        _logger.LogInformation("🧹 Cache de browsing e conexões limpos - próxima tentativa será fresh");
    }

    public void Dispose()
    {
        try
        {
            foreach (var connection in _serverConnections.Values)
            {
                try
                {
                    connection?.Disconnect();
                    connection?.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Erro ao desconectar servidor durante dispose");
                }
            }
            _serverConnections.Clear();
            _browseCache.Clear();
            _factory?.Dispose();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro durante dispose do OpcRealBrowsingService");
        }
    }
}
