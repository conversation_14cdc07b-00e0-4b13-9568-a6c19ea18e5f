@page "/logout"
@inject IAuthService AuthService
@inject NavigationManager Navigation
@inject ISnackbar Snackbar
@using AssetView.Nx.Modules.Auth.Services

<PageTitle>Logout - Smar Hart System</PageTitle>

@code {
    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Fazer logout
            await AuthService.LogoutAsync(string.Empty);
            
            Snackbar.Add("Logout realizado com sucesso!", Severity.Success);
            
            // Redirecionar para a página de login
            Navigation.NavigateTo("/login", true);
        }
        catch (Exception)
        {
            // Em caso de erro, ainda redirecionar para login
            Navigation.NavigateTo("/login", true);
        }
    }
}
