using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using AssetView.Nx.Core.Entities;
using AssetView.Nx.Core.Interfaces;
using AssetView.Nx.Infrastructure.Services;
using AssetView.Nx.Infrastructure.Repositories;

namespace AssetView.Nx.Infrastructure.Tests.Services;

public class PasswordResetTokenServiceTests
{
    private readonly Mock<IPasswordResetTokenRepository> _tokenRepositoryMock;
    private readonly Mock<IUnitOfWork> _unitOfWorkMock;
    private readonly Mock<ILogger<PasswordResetTokenService>> _loggerMock;
    private readonly PasswordResetTokenService _tokenService;

    public PasswordResetTokenServiceTests()
    {
        _tokenRepositoryMock = new Mock<IPasswordResetTokenRepository>();
        _unitOfWorkMock = new Mock<IUnitOfWork>();
        _loggerMock = new Mock<ILogger<PasswordResetTokenService>>();
        
        _tokenService = new PasswordResetTokenService(
            _tokenRepositoryMock.Object,
            _unitOfWorkMock.Object,
            _loggerMock.Object);
    }

    [Fact]
    public async Task GenerateTokenAsync_WhenUserCanRequestToken_ReturnsToken()
    {
        // Arrange
        var userId = "user123";
        var ipAddress = "***********";
        var userAgent = "Mozilla/5.0";

        _tokenRepositoryMock.Setup(x => x.CountValidTokensByUserIdAsync(userId))
                          .ReturnsAsync(0);
        _tokenRepositoryMock.Setup(x => x.GetTokensByIpAddressAsync(ipAddress, 1))
                          .ReturnsAsync(new List<PasswordResetToken>());

        // Act
        var result = await _tokenService.GenerateTokenAsync(userId, ipAddress, userAgent);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(userId, result.UserId);
        Assert.Equal(ipAddress, result.RequestIpAddress);
        Assert.Equal(userAgent, result.RequestUserAgent);
        Assert.False(result.IsUsed);
        Assert.True(result.ExpiresAt > DateTime.UtcNow);
    }

    [Fact]
    public async Task GenerateTokenAsync_WhenUserExceedsLimit_ThrowsException()
    {
        // Arrange
        var userId = "user123";
        
        _tokenRepositoryMock.Setup(x => x.CountValidTokensByUserIdAsync(userId))
                          .ReturnsAsync(5); // Exceeds limit

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(
            () => _tokenService.GenerateTokenAsync(userId));
    }

    [Fact]
    public async Task GenerateTokenAsync_WhenIpExceedsLimit_ThrowsException()
    {
        // Arrange
        var userId = "user123";
        var ipAddress = "***********";
        
        _tokenRepositoryMock.Setup(x => x.CountValidTokensByUserIdAsync(userId))
                          .ReturnsAsync(0);
        _tokenRepositoryMock.Setup(x => x.GetTokensByIpAddressAsync(ipAddress, 1))
                          .ReturnsAsync(Enumerable.Range(0, 10).Select(i => new PasswordResetToken()).ToList());

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(
            () => _tokenService.GenerateTokenAsync(userId, ipAddress));
    }

    [Fact]
    public async Task ValidateTokenAsync_WhenTokenValid_ReturnsToken()
    {
        // Arrange
        var token = "valid-token";
        var hashedToken = "hashed-token";
        var passwordResetToken = new PasswordResetToken
        {
            UserId = "user123",
            Token = hashedToken,
            ExpiresAt = DateTime.UtcNow.AddHours(1),
            IsUsed = false,
            IsDeleted = false
        };

        _tokenRepositoryMock.Setup(x => x.GetValidTokenAsync(It.IsAny<string>()))
                          .ReturnsAsync(passwordResetToken);

        // Act
        var result = await _tokenService.ValidateTokenAsync(token);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("user123", result.UserId);
    }

    [Fact]
    public async Task ValidateTokenAsync_WhenTokenInvalid_ReturnsNull()
    {
        // Arrange
        var token = "invalid-token";
        
        _tokenRepositoryMock.Setup(x => x.GetValidTokenAsync(It.IsAny<string>()))
                          .ReturnsAsync((PasswordResetToken?)null);

        // Act
        var result = await _tokenService.ValidateTokenAsync(token);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task ConsumeTokenAsync_WhenTokenValid_ReturnsTrue()
    {
        // Arrange
        var token = "valid-token";
        var passwordResetToken = new PasswordResetToken
        {
            UserId = "user123",
            Token = "hashed-token",
            ExpiresAt = DateTime.UtcNow.AddHours(1),
            IsUsed = false,
            IsDeleted = false
        };

        _tokenRepositoryMock.Setup(x => x.GetValidTokenAsync(It.IsAny<string>()))
                          .ReturnsAsync(passwordResetToken);

        // Act
        var result = await _tokenService.ConsumeTokenAsync(token, "***********", "Mozilla/5.0");

        // Assert
        Assert.True(result);
        Assert.True(passwordResetToken.IsUsed);
        Assert.NotNull(passwordResetToken.UsedAt);
        Assert.Equal("***********", passwordResetToken.UsedIpAddress);
        Assert.Equal("Mozilla/5.0", passwordResetToken.UsedUserAgent);
    }

    [Fact]
    public async Task ConsumeTokenAsync_WhenTokenInvalid_ReturnsFalse()
    {
        // Arrange
        var token = "invalid-token";
        
        _tokenRepositoryMock.Setup(x => x.GetValidTokenAsync(It.IsAny<string>()))
                          .ReturnsAsync((PasswordResetToken?)null);

        // Act
        var result = await _tokenService.ConsumeTokenAsync(token);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task CanRequestTokenAsync_WhenUserHasNoTokens_ReturnsTrue()
    {
        // Arrange
        var userId = "user123";
        
        _tokenRepositoryMock.Setup(x => x.CountValidTokensByUserIdAsync(userId))
                          .ReturnsAsync(0);

        // Act
        var result = await _tokenService.CanRequestTokenAsync(userId);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task CanRequestTokenAsync_WhenUserExceedsLimit_ReturnsFalse()
    {
        // Arrange
        var userId = "user123";
        
        _tokenRepositoryMock.Setup(x => x.CountValidTokensByUserIdAsync(userId))
                          .ReturnsAsync(5);

        // Act
        var result = await _tokenService.CanRequestTokenAsync(userId);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task CanRequestTokenByIpAsync_WhenIpHasNoTokens_ReturnsTrue()
    {
        // Arrange
        var ipAddress = "***********";
        
        _tokenRepositoryMock.Setup(x => x.GetTokensByIpAddressAsync(ipAddress, 1))
                          .ReturnsAsync(new List<PasswordResetToken>());

        // Act
        var result = await _tokenService.CanRequestTokenByIpAsync(ipAddress);

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task CanRequestTokenByIpAsync_WhenIpExceedsLimit_ReturnsFalse()
    {
        // Arrange
        var ipAddress = "***********";
        var tokens = Enumerable.Range(0, 10).Select(i => new PasswordResetToken()).ToList();
        
        _tokenRepositoryMock.Setup(x => x.GetTokensByIpAddressAsync(ipAddress, 1))
                          .ReturnsAsync(tokens);

        // Act
        var result = await _tokenService.CanRequestTokenByIpAsync(ipAddress);

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void GenerateSecureToken_ReturnsNonEmptyString()
    {
        // Act
        var token = _tokenService.GenerateSecureToken();

        // Assert
        Assert.NotNull(token);
        Assert.NotEmpty(token);
        Assert.True(token.Length > 20); // Should be reasonably long
    }

    [Fact]
    public void GenerateSecureToken_GeneratesUniqueTokens()
    {
        // Act
        var token1 = _tokenService.GenerateSecureToken();
        var token2 = _tokenService.GenerateSecureToken();

        // Assert
        Assert.NotEqual(token1, token2);
    }

    [Fact]
    public async Task CleanupExpiredTokensAsync_CallsRepository()
    {
        // Arrange
        _tokenRepositoryMock.Setup(x => x.CleanupExpiredTokensAsync())
                          .ReturnsAsync(5);

        // Act
        var result = await _tokenService.CleanupExpiredTokensAsync();

        // Assert
        Assert.Equal(5, result);
        _tokenRepositoryMock.Verify(x => x.CleanupExpiredTokensAsync(), Times.Once);
    }

    [Fact]
    public async Task InvalidateUserTokensAsync_CallsRepository()
    {
        // Arrange
        var userId = "user123";
        _tokenRepositoryMock.Setup(x => x.InvalidateUserTokensAsync(userId))
                          .ReturnsAsync(3);

        // Act
        var result = await _tokenService.InvalidateUserTokensAsync(userId);

        // Assert
        Assert.Equal(3, result);
        _tokenRepositoryMock.Verify(x => x.InvalidateUserTokensAsync(userId), Times.Once);
    }
}
