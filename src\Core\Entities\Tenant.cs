using AssetView.Nx.Core.Common;
using AssetView.Nx.Core.Enums;

namespace AssetView.Nx.Core.Entities;

/// <summary>
/// Entidade que representa um tenant no sistema
/// </summary>
public class Tenant : BaseEntity
{
    /// <summary>
    /// Nome do tenant
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Subdomínio único do tenant
    /// </summary>
    public string Subdomain { get; set; } = string.Empty;

    /// <summary>
    /// Estratégia de banco de dados do tenant
    /// </summary>
    public TenantDatabaseStrategy DatabaseStrategy { get; set; } = TenantDatabaseStrategy.Shared;

    /// <summary>
    /// String de conexão específica do tenant (quando DatabaseStrategy = Separate)
    /// </summary>
    public string? ConnectionString { get; set; }

    /// <summary>
    /// Indica se o tenant está ativo
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Configurações específicas do tenant em JSON
    /// </summary>
    public string? Settings { get; set; }

    /// <summary>
    /// Data de expiração da assinatura do tenant
    /// </summary>
    public DateTime? SubscriptionExpiresAt { get; set; }

    /// <summary>
    /// Limite máximo de usuários para o tenant
    /// </summary>
    public int? MaxUsers { get; set; }

    /// <summary>
    /// Limite máximo de projetos para o tenant
    /// </summary>
    public int? MaxProjects { get; set; }
}
