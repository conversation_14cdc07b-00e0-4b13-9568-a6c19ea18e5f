using AssetView.Nx.Modules.OpcDa.Interfaces;
using AssetView.Nx.Modules.OpcDa.Models;

namespace AssetView.Nx.Dashboard.Services;

/// <summary>
/// Serviço hospedado para manter o OpcDaService ativo no Dashboard
/// </summary>
public class OpcDaHostedService : BackgroundService
{
    private readonly IOpcDaService _opcDaService;
    private readonly ILogger<OpcDaHostedService> _logger;
    private readonly IServiceProvider _serviceProvider;
    private Timer? _healthCheckTimer;

    public OpcDaHostedService(
        IOpcDaService opcDaService,
        ILogger<OpcDaHostedService> logger,
        IServiceProvider serviceProvider)
    {
        _opcDaService = opcDaService;
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("=== OpcDaHostedService iniciado ===");

        // Configurar timer para verificação de saúde a cada 30 segundos
        _healthCheckTimer = new Timer(PerformHealthCheck, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));

        try
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    var status = _opcDaService.GetConnectionStatus();
                    _logger.LogDebug("Status do OpcDaService: {Status}", status);

                    // Se conectado, verificar se o monitoramento está ativo
                    if (status == OpcConnectionStatus.Connected)
                    {
                        _logger.LogDebug("OpcDaService conectado e ativo");
                    }

                    // Aguardar 60 segundos antes da próxima verificação
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("OpcDaHostedService cancelado");
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erro no loop principal do OpcDaHostedService");
                    await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro fatal no OpcDaHostedService");
        }
        finally
        {
            _healthCheckTimer?.Dispose();
            _logger.LogInformation("=== OpcDaHostedService finalizado ===");
        }
    }

    private void PerformHealthCheck(object? state)
    {
        try
        {
            var status = _opcDaService.GetConnectionStatus();
            _logger.LogDebug("Health Check - Status: {Status}", status);

            // Se conectado mas sem atividade, pode haver problema
            if (status == OpcConnectionStatus.Connected)
            {
                _logger.LogDebug("OpcDaService conectado e funcionando");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro no health check do OpcDaService");
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Parando OpcDaHostedService...");
        
        try
        {
            _healthCheckTimer?.Dispose();
            
            // Parar monitoramento se estiver ativo
            if (_opcDaService.GetConnectionStatus() == OpcConnectionStatus.Connected)
            {
                await _opcDaService.StopMonitoringAsync(cancellationToken);
                _logger.LogInformation("Monitoramento OPC parado");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao parar OpcDaHostedService");
        }

        await base.StopAsync(cancellationToken);
    }
}
