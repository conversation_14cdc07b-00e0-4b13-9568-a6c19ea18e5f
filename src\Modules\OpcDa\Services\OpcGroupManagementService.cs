using Microsoft.Extensions.Logging;
using AssetView.Nx.Modules.OpcDa.Interfaces;
using AssetView.Nx.Modules.OpcDa.Models;
using Opc;
using OpcCom;
using System.Collections.Concurrent;

namespace AssetView.Nx.Modules.OpcDa.Services;

/// <summary>
/// Serviço para gerenciamento de grupos OPC
/// </summary>
public class OpcGroupManagementService : IOpcGroupManagementService
{
    private readonly ILogger<OpcGroupManagementService> _logger;
    private readonly ConcurrentDictionary<string, OpcGroupManager> _groupManagers = new();
    private readonly ConcurrentDictionary<string, OpcCom.Da.Server> _serverConnections = new();

    public OpcGroupManagementService(ILogger<OpcGroupManagementService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Cria um novo grupo OPC
    /// </summary>
    public async Task<OpcGroupInfo> CreateGroupAsync(string serverProgId, OpcGroupConfig config, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Criando grupo OPC: {GroupName} no servidor: {ProgId}", config.Name, serverProgId);

            var server = await GetOrCreateServerConnection(serverProgId);
            if (server == null)
            {
                throw new InvalidOperationException($"Não foi possível conectar ao servidor: {serverProgId}");
            }

            var groupManager = GetOrCreateGroupManager(serverProgId);
            var groupInfo = await groupManager.CreateGroupAsync(server, config, cancellationToken);

            _logger.LogInformation("Grupo OPC criado com sucesso: {GroupName}", config.Name);
            return groupInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao criar grupo OPC: {GroupName}", config.Name);
            throw;
        }
    }

    /// <summary>
    /// Adiciona tags a um grupo existente
    /// </summary>
    public async Task<List<OpcTagSubscription>> AddTagsToGroupAsync(string serverProgId, string groupName, 
        List<string> tagPaths, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Adicionando {Count} tags ao grupo: {GroupName}", tagPaths.Count, groupName);

            var groupManager = GetGroupManager(serverProgId);
            if (groupManager == null)
            {
                throw new InvalidOperationException($"Gerenciador de grupo não encontrado para servidor: {serverProgId}");
            }

            var subscriptions = await groupManager.AddTagsAsync(groupName, tagPaths, cancellationToken);
            
            _logger.LogInformation("Adicionados {Count} tags ao grupo: {GroupName}", subscriptions.Count, groupName);
            return subscriptions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao adicionar tags ao grupo: {GroupName}", groupName);
            throw;
        }
    }

    /// <summary>
    /// Remove tags de um grupo
    /// </summary>
    public async Task RemoveTagsFromGroupAsync(string serverProgId, string groupName, 
        List<string> tagPaths, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Removendo {Count} tags do grupo: {GroupName}", tagPaths.Count, groupName);

            var groupManager = GetGroupManager(serverProgId);
            if (groupManager == null)
            {
                throw new InvalidOperationException($"Gerenciador de grupo não encontrado para servidor: {serverProgId}");
            }

            await groupManager.RemoveTagsAsync(groupName, tagPaths, cancellationToken);
            
            _logger.LogInformation("Removidos {Count} tags do grupo: {GroupName}", tagPaths.Count, groupName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao remover tags do grupo: {GroupName}", groupName);
            throw;
        }
    }

    /// <summary>
    /// Lista todos os grupos de um servidor
    /// </summary>
    public async Task<List<OpcGroupInfo>> GetGroupsAsync(string serverProgId, CancellationToken cancellationToken = default)
    {
        try
        {
            var groupManager = GetGroupManager(serverProgId);
            if (groupManager == null)
            {
                return new List<OpcGroupInfo>();
            }

            return await groupManager.GetAllGroupsAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao listar grupos do servidor: {ProgId}", serverProgId);
            return new List<OpcGroupInfo>();
        }
    }

    /// <summary>
    /// Obtém informações de um grupo específico
    /// </summary>
    public async Task<OpcGroupInfo?> GetGroupAsync(string serverProgId, string groupName, CancellationToken cancellationToken = default)
    {
        try
        {
            var groupManager = GetGroupManager(serverProgId);
            if (groupManager == null)
            {
                return null;
            }

            return await groupManager.GetGroupAsync(groupName, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter grupo: {GroupName}", groupName);
            return null;
        }
    }

    /// <summary>
    /// Ativa ou desativa um grupo
    /// </summary>
    public async Task SetGroupActiveAsync(string serverProgId, string groupName, bool active, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Definindo grupo {GroupName} como {Status}", groupName, active ? "ativo" : "inativo");

            var groupManager = GetGroupManager(serverProgId);
            if (groupManager == null)
            {
                throw new InvalidOperationException($"Gerenciador de grupo não encontrado para servidor: {serverProgId}");
            }

            await groupManager.SetGroupActiveAsync(groupName, active, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao definir status do grupo: {GroupName}", groupName);
            throw;
        }
    }

    /// <summary>
    /// Remove um grupo
    /// </summary>
    public async Task DeleteGroupAsync(string serverProgId, string groupName, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Removendo grupo: {GroupName}", groupName);

            var groupManager = GetGroupManager(serverProgId);
            if (groupManager == null)
            {
                throw new InvalidOperationException($"Gerenciador de grupo não encontrado para servidor: {serverProgId}");
            }

            await groupManager.DeleteGroupAsync(groupName, cancellationToken);
            
            _logger.LogInformation("Grupo removido com sucesso: {GroupName}", groupName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao remover grupo: {GroupName}", groupName);
            throw;
        }
    }

    /// <summary>
    /// Obtém estatísticas de um grupo
    /// </summary>
    public async Task<OpcGroupStatistics?> GetGroupStatisticsAsync(string serverProgId, string groupName, CancellationToken cancellationToken = default)
    {
        try
        {
            var groupManager = GetGroupManager(serverProgId);
            if (groupManager == null)
            {
                return null;
            }

            return await groupManager.GetGroupStatisticsAsync(groupName, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter estatísticas do grupo: {GroupName}", groupName);
            return null;
        }
    }

    /// <summary>
    /// Obtém ou cria conexão com servidor
    /// </summary>
    private async Task<OpcCom.Da.Server?> GetOrCreateServerConnection(string serverProgId)
    {
        if (_serverConnections.TryGetValue(serverProgId, out var existingServer))
        {
            return existingServer;
        }

        try
        {
            // Implementação simplificada - retornar grupo de exemplo
            // Em uma implementação real, seria necessário usar a API correta do OpcNetApi.Com
            _logger.LogInformation("Simulando criação de grupo: {GroupName}", "Grupo Simulado");

            // Retornar null para implementação simplificada
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao conectar com servidor: {ProgId}", serverProgId);
            return null;
        }
    }

    /// <summary>
    /// Obtém ou cria gerenciador de grupo
    /// </summary>
    private OpcGroupManager GetOrCreateGroupManager(string serverProgId)
    {
        return _groupManagers.GetOrAdd(serverProgId, _ => new OpcGroupManager(_logger));
    }

    /// <summary>
    /// Obtém gerenciador de grupo existente
    /// </summary>
    private OpcGroupManager? GetGroupManager(string serverProgId)
    {
        return _groupManagers.TryGetValue(serverProgId, out var manager) ? manager : null;
    }

    public void Dispose()
    {
        foreach (var manager in _groupManagers.Values)
        {
            manager.Dispose();
        }
        _groupManagers.Clear();

        foreach (var server in _serverConnections.Values)
        {
            try
            {
                // Limpeza simulada
                _logger.LogDebug("Limpeza de recursos simulada");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Erro ao desconectar servidor OPC");
            }
        }
        _serverConnections.Clear();
    }
}

/// <summary>
/// Gerenciador interno de grupos OPC
/// </summary>
internal class OpcGroupManager : IDisposable
{
    private readonly ILogger _logger;
    private readonly ConcurrentDictionary<string, OpcCom.Da.Subscription> _groups = new();
    private readonly ConcurrentDictionary<string, List<OpcTagSubscription>> _groupTags = new();

    public OpcGroupManager(ILogger logger)
    {
        _logger = logger;
    }

    public async Task<OpcGroupInfo> CreateGroupAsync(OpcCom.Da.Server server, OpcGroupConfig config, CancellationToken cancellationToken)
    {
        var state = new Opc.Da.SubscriptionState
        {
            Name = config.Name,
            Active = config.IsActive,
            UpdateRate = config.UpdateRateMs,
            KeepAlive = 0,
            Deadband = 0.0f,
            Locale = null
        };

        var subscription = (OpcCom.Da.Subscription)server.CreateSubscription(state);
        _groups.TryAdd(config.Name, subscription);
        _groupTags.TryAdd(config.Name, new List<OpcTagSubscription>());

        return new OpcGroupInfo
        {
            Name = config.Name,
            IsActive = config.IsActive,
            UpdateRateMs = config.UpdateRateMs,
            TagCount = 0,
            Category = config.Category,
            Description = config.Description,
            CreatedAt = DateTime.Now
        };
    }

    public async Task<List<OpcTagSubscription>> AddTagsAsync(string groupName, List<string> tagPaths, CancellationToken cancellationToken)
    {
        if (!_groups.TryGetValue(groupName, out var subscription))
        {
            throw new InvalidOperationException($"Grupo não encontrado: {groupName}");
        }

        var items = tagPaths.Select(path => new Opc.Da.Item { ItemName = path }).ToArray();
        var results = subscription.AddItems(items);

        var subscriptions = new List<OpcTagSubscription>();
        for (int i = 0; i < results.Length; i++)
        {
            if (results[i].ResultID.Succeeded())
            {
                var tagSub = new OpcTagSubscription
                {
                    TagPath = tagPaths[i],
                    GroupName = groupName,
                    IsActive = true,
                    ClientHandle = results[i].ClientHandle,
                    ServerHandle = results[i].ServerHandle
                };
                subscriptions.Add(tagSub);
            }
        }

        if (_groupTags.TryGetValue(groupName, out var existingTags))
        {
            existingTags.AddRange(subscriptions);
        }

        return subscriptions;
    }

    public async Task RemoveTagsAsync(string groupName, List<string> tagPaths, CancellationToken cancellationToken)
    {
        if (!_groups.TryGetValue(groupName, out var subscription) ||
            !_groupTags.TryGetValue(groupName, out var tags))
        {
            return;
        }

        var itemsToRemove = tags.Where(t => tagPaths.Contains(t.TagPath)).ToArray();
        if (itemsToRemove.Any())
        {
            var identifiers = itemsToRemove.Select(t => new Opc.ItemIdentifier { ServerHandle = t.ServerHandle }).ToArray();
            subscription.RemoveItems(identifiers);

            foreach (var item in itemsToRemove)
            {
                tags.Remove(item);
            }
        }
    }

    public async Task<List<OpcGroupInfo>> GetAllGroupsAsync(CancellationToken cancellationToken)
    {
        return _groups.Keys.Select(name => new OpcGroupInfo
        {
            Name = name,
            TagCount = _groupTags.TryGetValue(name, out var tags) ? tags.Count : 0
        }).ToList();
    }

    public async Task<OpcGroupInfo?> GetGroupAsync(string groupName, CancellationToken cancellationToken)
    {
        if (!_groups.ContainsKey(groupName))
            return null;

        return new OpcGroupInfo
        {
            Name = groupName,
            TagCount = _groupTags.TryGetValue(groupName, out var tags) ? tags.Count : 0
        };
    }

    public async Task SetGroupActiveAsync(string groupName, bool active, CancellationToken cancellationToken)
    {
        if (_groups.TryGetValue(groupName, out var subscription))
        {
            var state = subscription.GetState();
            state.Active = active;
            subscription.ModifyState((int)Opc.Da.StateMask.Active, state);
        }
    }

    public async Task DeleteGroupAsync(string groupName, CancellationToken cancellationToken)
    {
        if (_groups.TryRemove(groupName, out var subscription))
        {
            subscription.Dispose();
        }
        _groupTags.TryRemove(groupName, out _);
    }

    public async Task<OpcGroupStatistics?> GetGroupStatisticsAsync(string groupName, CancellationToken cancellationToken)
    {
        if (!_groupTags.TryGetValue(groupName, out var tags))
            return null;

        return new OpcGroupStatistics
        {
            GroupName = groupName,
            TotalTags = tags.Count,
            ActiveTags = tags.Count(t => t.IsActive),
            LastUpdateTime = DateTime.Now
        };
    }

    public void Dispose()
    {
        foreach (var subscription in _groups.Values)
        {
            try
            {
                subscription.Dispose();
            }
            catch { }
        }
        _groups.Clear();
        _groupTags.Clear();
    }
}
