namespace AssetView.Nx.Modules.OpcDa.Models;

/// <summary>
/// Opções de configuração para timeouts OPC
/// </summary>
public class OpcTimeoutOptions
{
    /// <summary>
    /// Timeout padrão em segundos
    /// </summary>
    public int DefaultTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Timeout para conexão em segundos
    /// </summary>
    public int ConnectionTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Timeout para desconexão em segundos
    /// </summary>
    public int DisconnectionTimeoutSeconds { get; set; } = 10;

    /// <summary>
    /// Timeout para leitura em segundos
    /// </summary>
    public int ReadTimeoutSeconds { get; set; } = 15;

    /// <summary>
    /// Timeout para escrita em segundos
    /// </summary>
    public int WriteTimeoutSeconds { get; set; } = 15;

    /// <summary>
    /// Timeout para navegação em segundos
    /// </summary>
    public int BrowseTimeoutSeconds { get; set; } = 20;

    /// <summary>
    /// Timeout para subscrição em segundos
    /// </summary>
    public int SubscribeTimeoutSeconds { get; set; } = 25;

    /// <summary>
    /// Timeout para cancelamento de subscrição em segundos
    /// </summary>
    public int UnsubscribeTimeoutSeconds { get; set; } = 10;

    /// <summary>
    /// Timeout para descoberta de servidores em segundos
    /// </summary>
    public int DiscoveryTimeoutSeconds { get; set; } = 45;

    /// <summary>
    /// Habilita retry automático em caso de timeout
    /// </summary>
    public bool EnableAutoRetry { get; set; } = true;

    /// <summary>
    /// Número máximo de tentativas
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Delay entre tentativas em segundos
    /// </summary>
    public int RetryDelaySeconds { get; set; } = 2;

    /// <summary>
    /// Multiplicador de delay para backoff exponencial
    /// </summary>
    public double RetryBackoffMultiplier { get; set; } = 1.5;
}

/// <summary>
/// Tipos de operação OPC
/// </summary>
public enum OpcOperationType
{
    Connect,
    Disconnect,
    Read,
    Write,
    Browse,
    Subscribe,
    Unsubscribe,
    Discovery,
    GroupCreate,
    GroupDelete,
    ItemAdd,
    ItemRemove,
    Other
}

/// <summary>
/// Timeout de operação OPC
/// </summary>
public class OpcOperationTimeout
{
    /// <summary>
    /// ID único da operação
    /// </summary>
    public string OperationId { get; set; } = string.Empty;

    /// <summary>
    /// Tipo da operação
    /// </summary>
    public OpcOperationType OperationType { get; set; }

    /// <summary>
    /// Hora de início da operação
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// Timeout configurado
    /// </summary>
    public TimeSpan Timeout { get; set; }

    /// <summary>
    /// Indica se a operação está ativa
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Hora de conclusão da operação
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// Indica se a operação foi bem-sucedida
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Indica se a operação expirou
    /// </summary>
    public bool TimedOut { get; set; }

    /// <summary>
    /// Mensagem de erro (se houver)
    /// </summary>
    public string? Error { get; set; }

    /// <summary>
    /// Token de cancelamento da operação
    /// </summary>
    public CancellationTokenSource? CancellationTokenSource { get; set; }

    /// <summary>
    /// Duração da operação
    /// </summary>
    public TimeSpan Duration => (CompletedAt ?? DateTime.Now) - StartTime;

    /// <summary>
    /// Progresso da operação (0-100%)
    /// </summary>
    public double Progress => Math.Min(100.0, Duration.TotalMilliseconds / Timeout.TotalMilliseconds * 100);
}

/// <summary>
/// Operação OPC ativa
/// </summary>
public class OpcActiveOperation
{
    /// <summary>
    /// ID da operação
    /// </summary>
    public string OperationId { get; set; } = string.Empty;

    /// <summary>
    /// Tipo da operação
    /// </summary>
    public OpcOperationType OperationType { get; set; }

    /// <summary>
    /// Hora de início
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// Tempo decorrido
    /// </summary>
    public TimeSpan ElapsedTime { get; set; }

    /// <summary>
    /// Tempo restante
    /// </summary>
    public TimeSpan RemainingTime { get; set; }

    /// <summary>
    /// Progresso da operação (0-100%)
    /// </summary>
    public double Progress { get; set; }

    /// <summary>
    /// Status da operação
    /// </summary>
    public string Status => Progress >= 100 ? "Expirando" : "Em andamento";
}

/// <summary>
/// Estatísticas de timeout
/// </summary>
public class OpcTimeoutStatistics
{
    /// <summary>
    /// Total de operações
    /// </summary>
    public int TotalOperations { get; set; }

    /// <summary>
    /// Operações ativas
    /// </summary>
    public int ActiveOperations { get; set; }

    /// <summary>
    /// Operações concluídas
    /// </summary>
    public int CompletedOperations { get; set; }

    /// <summary>
    /// Operações bem-sucedidas
    /// </summary>
    public int SuccessfulOperations { get; set; }

    /// <summary>
    /// Operações que expiraram
    /// </summary>
    public int TimedOutOperations { get; set; }

    /// <summary>
    /// Operações que falharam
    /// </summary>
    public int FailedOperations { get; set; }

    /// <summary>
    /// Duração média das operações
    /// </summary>
    public TimeSpan AverageOperationDuration { get; set; }

    /// <summary>
    /// Taxa de timeout (%)
    /// </summary>
    public double TimeoutRate { get; set; }

    /// <summary>
    /// Taxa de sucesso (%)
    /// </summary>
    public double SuccessRate => CompletedOperations > 0 ? 
        (double)SuccessfulOperations / CompletedOperations * 100 : 0.0;

    /// <summary>
    /// Operações por tipo
    /// </summary>
    public Dictionary<OpcOperationType, int> OperationsByType { get; set; } = new();

    /// <summary>
    /// Última atualização das estatísticas
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.Now;
}

/// <summary>
/// Argumentos do evento de timeout
/// </summary>
public class OpcTimeoutEventArgs : EventArgs
{
    /// <summary>
    /// ID da operação
    /// </summary>
    public string OperationId { get; set; } = string.Empty;

    /// <summary>
    /// Tipo da operação
    /// </summary>
    public OpcOperationType OperationType { get; set; }

    /// <summary>
    /// Duração da operação
    /// </summary>
    public TimeSpan Duration { get; set; }

    /// <summary>
    /// Indica se a operação foi bem-sucedida
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Indica se a operação expirou
    /// </summary>
    public bool TimedOut { get; set; }

    /// <summary>
    /// Mensagem de erro (se houver)
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Timestamp do evento
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;
}

/// <summary>
/// Exceção de timeout OPC
/// </summary>
public class OpcTimeoutException : Exception
{
    /// <summary>
    /// Tipo da operação que expirou
    /// </summary>
    public OpcOperationType OperationType { get; }

    /// <summary>
    /// Timeout configurado
    /// </summary>
    public TimeSpan Timeout { get; }

    public OpcTimeoutException(string message, OpcOperationType operationType, TimeSpan timeout) 
        : base(message)
    {
        OperationType = operationType;
        Timeout = timeout;
    }

    public OpcTimeoutException(string message, OpcOperationType operationType, TimeSpan timeout, Exception innerException) 
        : base(message, innerException)
    {
        OperationType = operationType;
        Timeout = timeout;
    }
}

/// <summary>
/// Configuração de retry para operações
/// </summary>
public class OpcRetryConfig
{
    /// <summary>
    /// Habilita retry automático
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Número máximo de tentativas
    /// </summary>
    public int MaxAttempts { get; set; } = 3;

    /// <summary>
    /// Delay inicial entre tentativas
    /// </summary>
    public TimeSpan InitialDelay { get; set; } = TimeSpan.FromSeconds(1);

    /// <summary>
    /// Multiplicador para backoff exponencial
    /// </summary>
    public double BackoffMultiplier { get; set; } = 2.0;

    /// <summary>
    /// Delay máximo entre tentativas
    /// </summary>
    public TimeSpan MaxDelay { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// Tipos de operação que devem ter retry
    /// </summary>
    public HashSet<OpcOperationType> RetryableOperations { get; set; } = new()
    {
        OpcOperationType.Connect,
        OpcOperationType.Read,
        OpcOperationType.Write,
        OpcOperationType.Discovery
    };
}
