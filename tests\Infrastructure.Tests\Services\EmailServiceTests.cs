using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using AssetView.Nx.Infrastructure.Services.Email;
using AssetView.Nx.Infrastructure.Services.Email.Models;

namespace AssetView.Nx.Infrastructure.Tests.Services;

public class EmailServiceTests
{
    private readonly Mock<ILogger<EmailService>> _loggerMock;
    private readonly Mock<IEmailTemplateService> _templateServiceMock;
    private readonly EmailSettings _emailSettings;

    public EmailServiceTests()
    {
        _loggerMock = new Mock<ILogger<EmailService>>();
        _templateServiceMock = new Mock<IEmailTemplateService>();
        _emailSettings = new EmailSettings
        {
            Enabled = true,
            SmtpServer = "smtp.test.com",
            SmtpPort = 587,
            EnableSsl = true,
            UserName = "<EMAIL>",
            Password = "password",
            FromEmail = "<EMAIL>",
            FromName = "Test System",
            TimeoutSeconds = 30,
            BaseUrl = "https://test.com"
        };
    }

    [Fact]
    public async Task IsConfiguredAsync_WhenSettingsValid_ReturnsTrue()
    {
        // Arrange
        var emailService = new EmailService(_emailSettings, _loggerMock.Object, _templateServiceMock.Object);

        // Act
        var result = await emailService.IsConfiguredAsync();

        // Assert
        Assert.True(result);
    }

    [Fact]
    public async Task IsConfiguredAsync_WhenServiceDisabled_ReturnsFalse()
    {
        // Arrange
        _emailSettings.Enabled = false;
        var emailService = new EmailService(_emailSettings, _loggerMock.Object, _templateServiceMock.Object);

        // Act
        var result = await emailService.IsConfiguredAsync();

        // Assert
        Assert.False(result);
    }

    [Fact]
    public async Task SendTemplateEmailAsync_WhenTemplateServiceNull_ReturnsFailure()
    {
        // Arrange
        var emailService = new EmailService(_emailSettings, _loggerMock.Object, null);

        // Act
        var result = await emailService.SendTemplateEmailAsync("<EMAIL>", "TestTemplate", new { }, "Test Subject");

        // Assert
        Assert.False(result.Success);
        Assert.Contains("não está disponível", result.Message);
    }

    [Fact]
    public async Task SendTemplateEmailAsync_WhenTemplateRenderFails_ReturnsFailure()
    {
        // Arrange
        var templateResult = TemplateResult.CreateFailure("Template not found");
        _templateServiceMock.Setup(x => x.RenderTemplateAsync(It.IsAny<string>(), It.IsAny<object>()))
                          .ReturnsAsync(templateResult);

        var emailService = new EmailService(_emailSettings, _loggerMock.Object, _templateServiceMock.Object);

        // Act
        var result = await emailService.SendTemplateEmailAsync("<EMAIL>", "TestTemplate", new { }, "Test Subject");

        // Assert
        Assert.False(result.Success);
        Assert.Contains("Erro ao processar template", result.Message);
    }

    [Fact]
    public async Task SendPasswordResetEmailAsync_CallsTemplateService()
    {
        // Arrange
        var templateResult = TemplateResult.CreateSuccess("<html>Test content</html>");
        _templateServiceMock.Setup(x => x.RenderTemplateAsync("PasswordReset", It.IsAny<object>()))
                          .ReturnsAsync(templateResult);

        var emailService = new EmailService(_emailSettings, _loggerMock.Object, _templateServiceMock.Object);

        // Act
        var result = await emailService.SendPasswordResetEmailAsync("<EMAIL>", "Test User", "https://test.com/reset");

        // Assert
        _templateServiceMock.Verify(x => x.RenderTemplateAsync("PasswordReset", It.Is<object>(data => 
            data.GetType().GetProperty("UserName")?.GetValue(data)?.ToString() == "Test User")), Times.Once);
    }

    [Fact]
    public async Task SendAccountConfirmationEmailAsync_CallsTemplateService()
    {
        // Arrange
        var templateResult = TemplateResult.CreateSuccess("<html>Test content</html>");
        _templateServiceMock.Setup(x => x.RenderTemplateAsync("AccountConfirmation", It.IsAny<object>()))
                          .ReturnsAsync(templateResult);

        var emailService = new EmailService(_emailSettings, _loggerMock.Object, _templateServiceMock.Object);

        // Act
        var result = await emailService.SendAccountConfirmationEmailAsync("<EMAIL>", "Test User", "https://test.com/confirm");

        // Assert
        _templateServiceMock.Verify(x => x.RenderTemplateAsync("AccountConfirmation", It.Is<object>(data => 
            data.GetType().GetProperty("UserName")?.GetValue(data)?.ToString() == "Test User")), Times.Once);
    }

    [Theory]
    [InlineData("")]
    [InlineData(null)]
    public async Task SendEmailAsync_WhenServiceDisabled_ReturnsFailure(string email)
    {
        // Arrange
        _emailSettings.Enabled = false;
        var emailService = new EmailService(_emailSettings, _loggerMock.Object, _templateServiceMock.Object);

        // Act
        var result = await emailService.SendEmailAsync("<EMAIL>", "Test Subject", "Test Body");

        // Assert
        Assert.False(result.Success);
        Assert.Contains("desabilitado", result.Message);
    }

    [Fact]
    public void EmailSettings_IsValid_WhenAllFieldsSet_ReturnsTrue()
    {
        // Act
        var result = _emailSettings.IsValid();

        // Assert
        Assert.True(result);
    }

    [Theory]
    [InlineData("", "smtp.test.com", 587, "user", "pass", "<EMAIL>", "From Name", "https://test.com")]
    [InlineData("smtp.test.com", "", 587, "user", "pass", "<EMAIL>", "From Name", "https://test.com")]
    [InlineData("smtp.test.com", "smtp.test.com", 0, "user", "pass", "<EMAIL>", "From Name", "https://test.com")]
    [InlineData("smtp.test.com", "smtp.test.com", 587, "", "pass", "<EMAIL>", "From Name", "https://test.com")]
    [InlineData("smtp.test.com", "smtp.test.com", 587, "user", "", "<EMAIL>", "From Name", "https://test.com")]
    [InlineData("smtp.test.com", "smtp.test.com", 587, "user", "pass", "", "From Name", "https://test.com")]
    [InlineData("smtp.test.com", "smtp.test.com", 587, "user", "pass", "<EMAIL>", "", "https://test.com")]
    [InlineData("smtp.test.com", "smtp.test.com", 587, "user", "pass", "<EMAIL>", "From Name", "")]
    public void EmailSettings_IsValid_WhenRequiredFieldMissing_ReturnsFalse(
        string smtpServer, string smtpServerParam, int smtpPort, string userName, 
        string password, string fromEmail, string fromName, string baseUrl)
    {
        // Arrange
        var settings = new EmailSettings
        {
            SmtpServer = smtpServer,
            SmtpPort = smtpPort,
            UserName = userName,
            Password = password,
            FromEmail = fromEmail,
            FromName = fromName,
            BaseUrl = baseUrl
        };

        // Act
        var result = settings.IsValid();

        // Assert
        Assert.False(result);
    }
}
