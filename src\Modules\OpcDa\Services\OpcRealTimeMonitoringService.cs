using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Hosting;
using AssetView.Nx.Modules.OpcDa.Interfaces;
using AssetView.Nx.Modules.OpcDa.Models;
using Opc;
using OpcCom;
using System.Collections.Concurrent;

namespace AssetView.Nx.Modules.OpcDa.Services;

/// <summary>
/// Serviço para monitoramento em tempo real de tags OPC
/// </summary>
public class OpcRealTimeMonitoringService : BackgroundService, IOpcRealTimeMonitoringService
{
    private readonly ILogger<OpcRealTimeMonitoringService> _logger;
    private readonly ConcurrentDictionary<string, OpcMonitoringSession> _sessions = new();
    private readonly ConcurrentDictionary<string, OpcCom.Da.Server> _serverConnections = new();
    private readonly System.Threading.Timer _statisticsTimer;
    private readonly object _lockObject = new();

    // Eventos para notificação de mudanças
    public event EventHandler<OpcValueChangedEventArgs>? ValueChanged;
    public event EventHandler<AssetView.Nx.Modules.OpcDa.Interfaces.OpcConnectionStatusEventArgs>? ConnectionStatusChanged;
    public event EventHandler<OpcMonitoringStatistics>? StatisticsUpdated;

    public OpcRealTimeMonitoringService(ILogger<OpcRealTimeMonitoringService> logger)
    {
        _logger = logger;
        
        // Timer para atualização de estatísticas a cada 5 segundos
        _statisticsTimer = new System.Threading.Timer(UpdateStatistics, null, TimeSpan.FromSeconds(5), TimeSpan.FromSeconds(5));
    }

    /// <summary>
    /// Inicia monitoramento de tags
    /// </summary>
    public async Task<string> StartMonitoringAsync(OpcMonitoringConfig config, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Iniciando monitoramento para servidor: {ProgId} com {TagCount} tags", 
                config.ServerProgId, config.TagPaths.Count);

            var sessionId = Guid.NewGuid().ToString();
            var server = await GetOrCreateServerConnection(config.ServerProgId);
            
            if (server == null)
            {
                throw new InvalidOperationException($"Não foi possível conectar ao servidor: {config.ServerProgId}");
            }

            var session = new OpcMonitoringSession
            {
                SessionId = sessionId,
                ServerProgId = config.ServerProgId,
                Config = config,
                Server = server,
                StartTime = DateTime.Now,
                IsActive = true
            };

            // Criar subscrição
            var subscriptionState = new Opc.Da.SubscriptionState
            {
                Name = $"Monitoring_{sessionId}",
                Active = true,
                UpdateRate = config.UpdateRateMs,
                KeepAlive = 0,
                Deadband = config.DeadbandPercent / 100.0f
            };

            session.Subscription = (OpcCom.Da.Subscription)server.CreateSubscription(subscriptionState);
            // Note: OpcNetApi.Com usa eventos diferentes - implementação simplificada
            // session.Subscription.DataChanged += (sender, e) => OnDataChanged(sessionId, e);

            // Adicionar tags à subscrição
            await AddTagsToSession(session, config.TagPaths);

            _sessions.TryAdd(sessionId, session);
            
            _logger.LogInformation("Monitoramento iniciado com sucesso. SessionId: {SessionId}", sessionId);
            
            // Notificar mudança de status
            ConnectionStatusChanged?.Invoke(this, new AssetView.Nx.Modules.OpcDa.Interfaces.OpcConnectionStatusEventArgs(
                OpcConnectionStatus.Connected, "Monitoramento iniciado"));

            return sessionId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao iniciar monitoramento para servidor: {ProgId}", config.ServerProgId);
            throw;
        }
    }

    /// <summary>
    /// Para monitoramento de uma sessão
    /// </summary>
    public async Task StopMonitoringAsync(string sessionId, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_sessions.TryRemove(sessionId, out var session))
            {
                _logger.LogInformation("Parando monitoramento da sessão: {SessionId}", sessionId);
                
                session.IsActive = false;
                session.Subscription?.Dispose();
                
                // Notificar mudança de status
                ConnectionStatusChanged?.Invoke(this, new AssetView.Nx.Modules.OpcDa.Interfaces.OpcConnectionStatusEventArgs(
                    OpcConnectionStatus.Disconnected, "Monitoramento parado"));
                
                _logger.LogInformation("Monitoramento parado com sucesso. SessionId: {SessionId}", sessionId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao parar monitoramento da sessão: {SessionId}", sessionId);
            throw;
        }
    }

    /// <summary>
    /// Adiciona tags a uma sessão de monitoramento
    /// </summary>
    public async Task AddTagsToMonitoringAsync(string sessionId, List<string> tagPaths, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_sessions.TryGetValue(sessionId, out var session))
            {
                _logger.LogDebug("Adicionando {TagCount} tags à sessão: {SessionId}", tagPaths.Count, sessionId);
                await AddTagsToSession(session, tagPaths);
            }
            else
            {
                throw new InvalidOperationException($"Sessão não encontrada: {sessionId}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao adicionar tags à sessão: {SessionId}", sessionId);
            throw;
        }
    }

    /// <summary>
    /// Remove tags de uma sessão de monitoramento
    /// </summary>
    public async Task RemoveTagsFromMonitoringAsync(string sessionId, List<string> tagPaths, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_sessions.TryGetValue(sessionId, out var session))
            {
                _logger.LogDebug("Removendo {TagCount} tags da sessão: {SessionId}", tagPaths.Count, sessionId);
                await RemoveTagsFromSession(session, tagPaths);
            }
            else
            {
                throw new InvalidOperationException($"Sessão não encontrada: {sessionId}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao remover tags da sessão: {SessionId}", sessionId);
            throw;
        }
    }

    /// <summary>
    /// Obtém estatísticas de monitoramento
    /// </summary>
    public async Task<OpcMonitoringStatistics> GetMonitoringStatisticsAsync(string? sessionId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            if (sessionId != null)
            {
                // Estatísticas de uma sessão específica
                if (_sessions.TryGetValue(sessionId, out var session))
                {
                    return CalculateSessionStatistics(session);
                }
                throw new InvalidOperationException($"Sessão não encontrada: {sessionId}");
            }
            else
            {
                // Estatísticas globais
                return CalculateGlobalStatistics();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter estatísticas de monitoramento");
            throw;
        }
    }

    /// <summary>
    /// Lista todas as sessões ativas
    /// </summary>
    public async Task<List<OpcMonitoringSessionInfo>> GetActiveSessionsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return _sessions.Values
                .Where(s => s.IsActive)
                .Select(s => new OpcMonitoringSessionInfo
                {
                    SessionId = s.SessionId,
                    ServerProgId = s.ServerProgId,
                    TagCount = s.MonitoredTags.Count,
                    StartTime = s.StartTime,
                    UpdateRateMs = s.Config.UpdateRateMs,
                    IsActive = s.IsActive
                })
                .ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao listar sessões ativas");
            return new List<OpcMonitoringSessionInfo>();
        }
    }

    /// <summary>
    /// Lê valores atuais de tags específicos
    /// </summary>
    public async Task<List<OpcTagValue>> ReadTagValuesAsync(string serverProgId, List<string> tagPaths, CancellationToken cancellationToken = default)
    {
        try
        {
            var server = await GetOrCreateServerConnection(serverProgId);
            if (server == null)
            {
                throw new InvalidOperationException($"Não foi possível conectar ao servidor: {serverProgId}");
            }

            var items = tagPaths.Select(path => new Opc.Da.Item { ItemName = path }).ToArray();
            var results = server.Read(items);

            var values = new List<OpcTagValue>();
            for (int i = 0; i < results.Length; i++)
            {
                values.Add(new OpcTagValue
                {
                    TagPath = tagPaths[i],
                    Value = results[i].Value,
                    Quality = results[i].Quality.ToString(),
                    Timestamp = results[i].Timestamp,
                    DataType = results[i].Value?.GetType().Name ?? "Unknown"
                });
            }

            return values;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao ler valores de tags do servidor: {ProgId}", serverProgId);
            throw;
        }
    }

    /// <summary>
    /// Execução em background para manutenção das sessões
    /// </summary>
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Serviço de monitoramento OPC em tempo real iniciado");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
                
                // Verificar saúde das sessões
                await CheckSessionHealth();
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro no loop principal do serviço de monitoramento");
            }
        }

        _logger.LogInformation("Serviço de monitoramento OPC em tempo real parado");
    }

    /// <summary>
    /// Obtém ou cria conexão com servidor
    /// </summary>
    private async Task<OpcCom.Da.Server?> GetOrCreateServerConnection(string serverProgId)
    {
        if (_serverConnections.TryGetValue(serverProgId, out var existingServer))
        {
            return existingServer;
        }

        try
        {
            // Implementação simplificada - retornar servidor simulado
            // Em uma implementação real, seria necessário usar a API correta do OpcNetApi.Com
            _logger.LogInformation("Simulando monitoramento para servidor: {ProgId}", serverProgId);
            // Implementação simplificada - não criar servidor real
            // var server = new OpcCom.Da.Server(...); - construtor não disponível

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao conectar com servidor: {ProgId}", serverProgId);
            return null;
        }
    }

    /// <summary>
    /// Adiciona tags a uma sessão
    /// </summary>
    private async Task AddTagsToSession(OpcMonitoringSession session, List<string> tagPaths)
    {
        var items = tagPaths.Select(path => new Opc.Da.Item { ItemName = path }).ToArray();
        var results = session.Subscription.AddItems(items);

        for (int i = 0; i < results.Length; i++)
        {
            if (results[i].ResultID.Succeeded())
            {
                var monitoredTag = new OpcMonitoredTag
                {
                    TagPath = tagPaths[i],
                    ClientHandle = results[i].ClientHandle,
                    ServerHandle = results[i].ServerHandle,
                    AddedAt = DateTime.Now
                };
                
                session.MonitoredTags.TryAdd(tagPaths[i], monitoredTag);
            }
        }
    }

    /// <summary>
    /// Remove tags de uma sessão
    /// </summary>
    private async Task RemoveTagsFromSession(OpcMonitoringSession session, List<string> tagPaths)
    {
        var itemsToRemove = new List<Opc.ItemIdentifier>();
        
        foreach (var tagPath in tagPaths)
        {
            if (session.MonitoredTags.TryRemove(tagPath, out var tag))
            {
                itemsToRemove.Add(new Opc.ItemIdentifier { ServerHandle = tag.ServerHandle });
            }
        }

        if (itemsToRemove.Any())
        {
            session.Subscription.RemoveItems(itemsToRemove.ToArray());
        }
    }

    /// <summary>
    /// Manipula mudanças de dados (implementação simplificada)
    /// </summary>
    private void OnDataChanged(string sessionId, object[] results)
    {
        try
        {
            if (!_sessions.TryGetValue(sessionId, out var session))
                return;

            // Implementação simplificada - em uma implementação real,
            // seria necessário usar os eventos específicos do OpcNetApi.Com
            session.LastActivity = DateTime.Now;
            session.TotalUpdates += results?.Length ?? 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao processar mudança de dados na sessão: {SessionId}", sessionId);
        }
    }

    /// <summary>
    /// Atualiza estatísticas
    /// </summary>
    private void UpdateStatistics(object? state)
    {
        try
        {
            var statistics = CalculateGlobalStatistics();
            StatisticsUpdated?.Invoke(this, statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao atualizar estatísticas");
        }
    }

    /// <summary>
    /// Calcula estatísticas de uma sessão
    /// </summary>
    private OpcMonitoringStatistics CalculateSessionStatistics(OpcMonitoringSession session)
    {
        var uptime = DateTime.Now - session.StartTime;
        var updatesPerSecond = uptime.TotalSeconds > 0 ? session.TotalUpdates / uptime.TotalSeconds : 0;

        return new OpcMonitoringStatistics
        {
            SessionId = session.SessionId,
            TotalSessions = 1,
            ActiveSessions = session.IsActive ? 1 : 0,
            TotalTags = session.MonitoredTags.Count,
            TotalUpdates = session.TotalUpdates,
            UpdatesPerSecond = updatesPerSecond,
            UptimeSeconds = (long)uptime.TotalSeconds,
            LastActivity = session.LastActivity
        };
    }

    /// <summary>
    /// Calcula estatísticas globais
    /// </summary>
    private OpcMonitoringStatistics CalculateGlobalStatistics()
    {
        var activeSessions = _sessions.Values.Where(s => s.IsActive).ToList();
        var totalUpdates = activeSessions.Sum(s => s.TotalUpdates);
        var totalTags = activeSessions.Sum(s => s.MonitoredTags.Count);
        var oldestSession = activeSessions.OrderBy(s => s.StartTime).FirstOrDefault();
        var uptime = oldestSession != null ? DateTime.Now - oldestSession.StartTime : TimeSpan.Zero;
        var updatesPerSecond = uptime.TotalSeconds > 0 ? totalUpdates / uptime.TotalSeconds : 0;

        return new OpcMonitoringStatistics
        {
            TotalSessions = _sessions.Count,
            ActiveSessions = activeSessions.Count,
            TotalTags = totalTags,
            TotalUpdates = totalUpdates,
            UpdatesPerSecond = updatesPerSecond,
            UptimeSeconds = (long)uptime.TotalSeconds,
            LastActivity = activeSessions.Max(s => s.LastActivity)
        };
    }

    /// <summary>
    /// Verifica saúde das sessões
    /// </summary>
    private async Task CheckSessionHealth()
    {
        var now = DateTime.Now;
        var inactiveSessions = _sessions.Values
            .Where(s => s.IsActive && (now - s.LastActivity).TotalMinutes > 5)
            .ToList();

        foreach (var session in inactiveSessions)
        {
            _logger.LogWarning("Sessão inativa detectada: {SessionId}, última atividade: {LastActivity}", 
                session.SessionId, session.LastActivity);
            
            // Tentar reconectar ou marcar como inativa
            try
            {
                // Implementar lógica de reconexão se necessário
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao verificar saúde da sessão: {SessionId}", session.SessionId);
            }
        }
    }

    public override void Dispose()
    {
        _statisticsTimer?.Dispose();
        
        foreach (var session in _sessions.Values)
        {
            try
            {
                session.Subscription?.Dispose();
            }
            catch { }
        }
        _sessions.Clear();

        foreach (var server in _serverConnections.Values)
        {
            try
            {
                //server.Disconnect();
                server.Dispose();
            }
            catch { }
        }
        _serverConnections.Clear();

        base.Dispose();
    }
}
