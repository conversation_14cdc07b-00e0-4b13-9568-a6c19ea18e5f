namespace AssetView.Nx.Modules.OpcDa.Models;

/// <summary>
/// Opções de configuração para retry automático OPC
/// </summary>
public class OpcRetryOptions
{
    /// <summary>
    /// Habilita retry padrão
    /// </summary>
    public bool EnableDefaultRetry { get; set; } = true;

    /// <summary>
    /// Número máximo de tentativas padrão
    /// </summary>
    public int DefaultMaxAttempts { get; set; } = 3;

    /// <summary>
    /// Delay padrão entre tentativas em segundos
    /// </summary>
    public int DefaultRetryDelaySeconds { get; set; } = 2;

    /// <summary>
    /// Multiplicador de backoff padrão
    /// </summary>
    public double DefaultBackoffMultiplier { get; set; } = 1.5;

    // Configurações específicas para conexão
    public bool EnableConnectionRetry { get; set; } = true;
    public int ConnectionMaxAttempts { get; set; } = 5;
    public int ConnectionRetryDelaySeconds { get; set; } = 3;
    public double ConnectionBackoffMultiplier { get; set; } = 2.0;

    // Configurações específicas para leitura
    public bool EnableReadRetry { get; set; } = true;
    public int ReadMaxAttempts { get; set; } = 3;
    public int ReadRetryDelaySeconds { get; set; } = 1;
    public double ReadBackoffMultiplier { get; set; } = 1.5;

    // Configurações específicas para escrita
    public bool EnableWriteRetry { get; set; } = true;
    public int WriteMaxAttempts { get; set; } = 3;
    public int WriteRetryDelaySeconds { get; set; } = 1;
    public double WriteBackoffMultiplier { get; set; } = 1.5;

    // Configurações específicas para descoberta
    public bool EnableDiscoveryRetry { get; set; } = true;
    public int DiscoveryMaxAttempts { get; set; } = 2;
    public int DiscoveryRetryDelaySeconds { get; set; } = 5;
    public double DiscoveryBackoffMultiplier { get; set; } = 2.0;

    /// <summary>
    /// Delay máximo entre tentativas em segundos
    /// </summary>
    public int MaxRetryDelaySeconds { get; set; } = 60;

    /// <summary>
    /// Timeout total para todas as tentativas em segundos
    /// </summary>
    public int TotalRetryTimeoutSeconds { get; set; } = 300;
}

/// <summary>
/// Tentativa de retry OPC
/// </summary>
public class OpcRetryAttempt
{
    /// <summary>
    /// ID único da operação
    /// </summary>
    public string OperationId { get; set; } = string.Empty;

    /// <summary>
    /// Tipo da operação
    /// </summary>
    public OpcOperationType OperationType { get; set; }

    /// <summary>
    /// Configuração de retry
    /// </summary>
    public OpcRetryConfig Config { get; set; } = new();

    /// <summary>
    /// Hora de início da primeira tentativa
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// Hora da última tentativa
    /// </summary>
    public DateTime? LastAttemptTime { get; set; }

    /// <summary>
    /// Hora de conclusão
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// Número da tentativa atual
    /// </summary>
    public int AttemptCount { get; set; }

    /// <summary>
    /// Número máximo de tentativas
    /// </summary>
    public int MaxAttempts { get; set; }

    /// <summary>
    /// Indica se a operação foi bem-sucedida
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Indica se a operação foi concluída
    /// </summary>
    public bool IsCompleted { get; set; }

    /// <summary>
    /// Indica se está aguardando para próxima tentativa
    /// </summary>
    public bool IsWaitingForRetry { get; set; }

    /// <summary>
    /// Hora da próxima tentativa
    /// </summary>
    public DateTime? NextRetryTime { get; set; }

    /// <summary>
    /// Lista de erros das tentativas
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Token de cancelamento
    /// </summary>
    public CancellationTokenSource? CancellationTokenSource { get; set; }

    /// <summary>
    /// Duração total da operação
    /// </summary>
    public TimeSpan Duration => (CompletedAt ?? DateTime.Now) - StartTime;

    /// <summary>
    /// Progresso das tentativas (0-100%)
    /// </summary>
    public double Progress => MaxAttempts > 0 ? (double)AttemptCount / MaxAttempts * 100 : 0.0;
}

/// <summary>
/// Tentativa ativa de retry
/// </summary>
public class OpcActiveRetryAttempt
{
    /// <summary>
    /// ID da operação
    /// </summary>
    public string OperationId { get; set; } = string.Empty;

    /// <summary>
    /// Tipo da operação
    /// </summary>
    public OpcOperationType OperationType { get; set; }

    /// <summary>
    /// Tentativa atual
    /// </summary>
    public int CurrentAttempt { get; set; }

    /// <summary>
    /// Máximo de tentativas
    /// </summary>
    public int MaxAttempts { get; set; }

    /// <summary>
    /// Hora de início
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// Hora da última tentativa
    /// </summary>
    public DateTime? LastAttemptTime { get; set; }

    /// <summary>
    /// Hora da próxima tentativa
    /// </summary>
    public DateTime? NextRetryTime { get; set; }

    /// <summary>
    /// Tempo decorrido
    /// </summary>
    public TimeSpan ElapsedTime { get; set; }

    /// <summary>
    /// Indica se está aguardando para retry
    /// </summary>
    public bool IsWaitingForRetry { get; set; }

    /// <summary>
    /// Tempo restante para próxima tentativa
    /// </summary>
    public TimeSpan? TimeToNextRetry => NextRetryTime.HasValue ? 
        NextRetryTime.Value - DateTime.Now : null;

    /// <summary>
    /// Status da tentativa
    /// </summary>
    public string Status => IsWaitingForRetry ? "Aguardando retry" : "Executando";
}

/// <summary>
/// Estatísticas de retry
/// </summary>
public class OpcRetryStatistics
{
    /// <summary>
    /// Total de operações
    /// </summary>
    public int TotalOperations { get; set; }

    /// <summary>
    /// Operações concluídas
    /// </summary>
    public int CompletedOperations { get; set; }

    /// <summary>
    /// Operações bem-sucedidas
    /// </summary>
    public int SuccessfulOperations { get; set; }

    /// <summary>
    /// Operações que falharam
    /// </summary>
    public int FailedOperations { get; set; }

    /// <summary>
    /// Total de tentativas de retry
    /// </summary>
    public long TotalRetryAttempts { get; set; }

    /// <summary>
    /// Média de tentativas por operação
    /// </summary>
    public double AverageAttemptsPerOperation { get; set; }

    /// <summary>
    /// Taxa de sucesso após retry (%)
    /// </summary>
    public double SuccessRateAfterRetry { get; set; }

    /// <summary>
    /// Taxa de sucesso do retry (%)
    /// </summary>
    public double RetrySuccessRate { get; set; }

    /// <summary>
    /// Operações por tipo
    /// </summary>
    public Dictionary<OpcOperationType, int> OperationsByType { get; set; } = new();

    /// <summary>
    /// Última atualização das estatísticas
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.Now;

    /// <summary>
    /// Eficiência do retry
    /// </summary>
    public string RetryEfficiency => RetrySuccessRate switch
    {
        >= 80 => "Excelente",
        >= 60 => "Boa",
        >= 40 => "Regular",
        >= 20 => "Baixa",
        _ => "Muito Baixa"
    };
}

/// <summary>
/// Argumentos do evento de retry
/// </summary>
public class OpcRetryEventArgs : EventArgs
{
    /// <summary>
    /// ID da operação
    /// </summary>
    public string OperationId { get; set; } = string.Empty;

    /// <summary>
    /// Tipo da operação
    /// </summary>
    public OpcOperationType OperationType { get; set; }

    /// <summary>
    /// Número da tentativa
    /// </summary>
    public int AttemptNumber { get; set; }

    /// <summary>
    /// Máximo de tentativas
    /// </summary>
    public int MaxAttempts { get; set; }

    /// <summary>
    /// Indica se é uma tentativa de retry (não a primeira)
    /// </summary>
    public bool IsRetry { get; set; }

    /// <summary>
    /// Indica se a operação foi bem-sucedida
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Duração da operação
    /// </summary>
    public TimeSpan Duration { get; set; }

    /// <summary>
    /// Mensagem de erro (se houver)
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Timestamp do evento
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;
}

/// <summary>
/// Política de retry personalizada
/// </summary>
public class OpcRetryPolicy
{
    /// <summary>
    /// Nome da política
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Descrição da política
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Configurações por tipo de operação
    /// </summary>
    public Dictionary<OpcOperationType, OpcRetryConfig> OperationConfigs { get; set; } = new();

    /// <summary>
    /// Configuração padrão
    /// </summary>
    public OpcRetryConfig DefaultConfig { get; set; } = new();

    /// <summary>
    /// Indica se é uma política do sistema
    /// </summary>
    public bool IsSystemPolicy { get; set; }
}

/// <summary>
/// Políticas de retry predefinidas
/// </summary>
public static class OpcRetryPolicies
{
    public static readonly OpcRetryPolicy Conservative = new()
    {
        Name = "Conservadora",
        Description = "Política conservadora com poucos retries",
        DefaultConfig = new OpcRetryConfig
        {
            Enabled = true,
            MaxAttempts = 2,
            InitialDelay = TimeSpan.FromSeconds(1),
            BackoffMultiplier = 1.5
        },
        IsSystemPolicy = true
    };

    public static readonly OpcRetryPolicy Aggressive = new()
    {
        Name = "Agressiva",
        Description = "Política agressiva com muitos retries",
        DefaultConfig = new OpcRetryConfig
        {
            Enabled = true,
            MaxAttempts = 5,
            InitialDelay = TimeSpan.FromSeconds(0.5),
            BackoffMultiplier = 2.0
        },
        IsSystemPolicy = true
    };

    public static readonly OpcRetryPolicy Balanced = new()
    {
        Name = "Balanceada",
        Description = "Política balanceada entre performance e confiabilidade",
        DefaultConfig = new OpcRetryConfig
        {
            Enabled = true,
            MaxAttempts = 3,
            InitialDelay = TimeSpan.FromSeconds(1),
            BackoffMultiplier = 1.8
        },
        IsSystemPolicy = true
    };

    public static List<OpcRetryPolicy> GetAllPolicies()
    {
        return new List<OpcRetryPolicy> { Conservative, Balanced, Aggressive };
    }
}
