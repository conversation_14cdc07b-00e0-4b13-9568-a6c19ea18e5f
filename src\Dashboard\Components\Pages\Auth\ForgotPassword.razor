@page "/forgot-password"
@layout AuthLayout
@using AssetView.Nx.Modules.Auth.Services
@using MudBlazor
@*using System.ComponentModel.DataAnnotations*@
@inject IAuthService AuthService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>Recuperação de Senha - Smar Hart System</PageTitle>

<MudContainer MaxWidth="MaxWidth.Small" Class="mt-16">
    <MudPaper Elevation="4" Class="pa-8">
        <MudStack Spacing="4">
            <!-- Header -->
            <MudStack AlignItems="AlignItems.Center" Spacing="2">
                <MudIcon Icon="@Icons.Material.Filled.Lock" Size="Size.Large" Color="Color.Primary" />
                <MudText Typo="Typo.h4" Align="Align.Center" Color="Color.Primary">
                    Recuperação de Senha
                </MudText>
                <MudText Typo="Typo.body1" Align="Align.Center" Color="Color.Secondary">
                    Digite seu e-mail para receber um link de recuperação
                </MudText>
            </MudStack>

            <!-- Formulário -->
            <EditForm Model="@_model" OnValidSubmit="@HandleSubmit">
                <DataAnnotationsValidator />
                
                <MudStack Spacing="3">
                    <MudTextField @bind-Value="_model.Email"
                                  Label="E-mail"
                                  Variant="Variant.Outlined"
                                  InputType="InputType.Email"
                                  Adornment="Adornment.Start"
                                  AdornmentIcon="@Icons.Material.Filled.Email"
                                  Required="true"
                                  RequiredError="E-mail é obrigatório"
                                  For="@(() => _model.Email)"
                                  Disabled="@_isLoading"
                                  Validation="@(new Func<string, string?>(ValidateEmail))" />

                    <MudButton ButtonType="ButtonType.Submit"
                               Variant="Variant.Filled"
                               Color="Color.Primary"
                               Size="Size.Large"
                               FullWidth="true"
                               StartIcon="@Icons.Material.Filled.Send"
                               loading="@_isLoading"
                               loading-text="Enviando...">
                        Enviar Link de Recuperação
                    </MudButton>
                </MudStack>
            </EditForm>

            <!-- Links de navegação -->
            <MudDivider />
            
            <MudStack Row="true" Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                <MudButton Href="/login"
                           Variant="Variant.Text"
                           Color="Color.Primary"
                           StartIcon="@Icons.Material.Filled.ArrowBack">
                    Voltar ao Login
                </MudButton>
                
                <MudButton Href="/register"
                           Variant="Variant.Text"
                           Color="Color.Secondary">
                    Criar Conta
                </MudButton>
            </MudStack>
        </MudStack>
    </MudPaper>
</MudContainer>

@code {
    private readonly ForgotPasswordModel _model = new();
    private bool _isLoading = false;

    private async Task HandleSubmit()
    {
        if (_isLoading) return;

        _isLoading = true;
        StateHasChanged();

        try
        {
            var result = await AuthService.ForgotPasswordAsync(_model.Email);

            if (result.Success)
            {
                Snackbar.Add(result.Message ?? "Link de recuperação enviado com sucesso!", Severity.Success);
                Navigation.NavigateTo("/forgot-password-confirmation");
            }
            else
            {
                Snackbar.Add(result.Message ?? "Erro ao enviar link de recuperação", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add("Erro interno. Tente novamente mais tarde.", Severity.Error);
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private string? ValidateEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email))
            return "E-mail é obrigatório";

        if (!email.Contains("@") || !email.Contains("."))
            return "Formato de e-mail inválido";

        return null;
    }

    public class ForgotPasswordModel
    {
        public string Email { get; set; } = string.Empty;
    }
}
