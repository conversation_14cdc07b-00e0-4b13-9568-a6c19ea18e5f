using System.ComponentModel.DataAnnotations;

namespace AssetView.Nx.Modules.OpcDa.Models;

/// <summary>
/// Representa um item de dados OPC DA
/// </summary>
public class OpcDataItem
{
    /// <summary>
    /// Identificador único do item
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Nome do tag OPC
    /// </summary>
    [Required]
    public string TagName { get; set; } = string.Empty;

    /// <summary>
    /// Valor atual do item
    /// </summary>
    public object? Value { get; set; }

    /// <summary>
    /// Qualidade do valor (Good, Bad, Uncertain)
    /// </summary>
    public OpcQuality Quality { get; set; } = OpcQuality.Bad;

    /// <summary>
    /// Timestamp da última atualização
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Tipo de dados do valor
    /// </summary>
    public Type? DataType { get; set; }

    /// <summary>
    /// Indica se o item está ativo para leitura
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Grupo ao qual o item pertence
    /// </summary>
    public string? GroupName { get; set; }

    /// <summary>
    /// Descrição do item
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Unidade de medida
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    /// Valor mínimo esperado
    /// </summary>
    public double? MinValue { get; set; }

    /// <summary>
    /// Valor máximo esperado
    /// </summary>
    public double? MaxValue { get; set; }
}

/// <summary>
/// Qualidade dos dados OPC
/// </summary>
public enum OpcQuality
{
    /// <summary>
    /// Qualidade ruim - dados não confiáveis
    /// </summary>
    Bad = 0,

    /// <summary>
    /// Qualidade incerta - dados podem não ser confiáveis
    /// </summary>
    Uncertain = 64,

    /// <summary>
    /// Qualidade boa - dados confiáveis
    /// </summary>
    Good = 192
}

/// <summary>
/// Status da conexão OPC
/// </summary>
public enum OpcConnectionStatus
{
    /// <summary>
    /// Desconectado
    /// </summary>
    Disconnected,

    /// <summary>
    /// Conectando
    /// </summary>
    Connecting,

    /// <summary>
    /// Conectado
    /// </summary>
    Connected,

    /// <summary>
    /// Erro de conexão
    /// </summary>
    Error
}
