using AssetView.Nx.Core.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace AssetView.Nx.Infrastructure.Middleware;

/// <summary>
/// Middleware para resolução automática de tenant baseado no subdomínio ou header
/// </summary>
public class TenantResolutionMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<TenantResolutionMiddleware> _logger;

    public TenantResolutionMiddleware(RequestDelegate next, ILogger<TenantResolutionMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context, ITenantService tenantService)
    {
        var tenantIdentifier = ResolveTenantIdentifier(context);
        
        if (!string.IsNullOrEmpty(tenantIdentifier))
        {
            var tenant = await tenantService.GetTenantBySubdomainAsync(tenantIdentifier);
            
            if (tenant != null && tenant.IsActive)
            {
                tenantService.SetCurrentTenant(tenant.Id);
                context.Items["TenantId"] = tenant.Id;
                context.Items["TenantName"] = tenant.Name;
                
                _logger.LogInformation("Tenant resolved: {TenantId} ({TenantName})", tenant.Id, tenant.Name);
            }
            else
            {
                _logger.LogWarning("Tenant not found or inactive: {TenantIdentifier}", tenantIdentifier);
            }
        }
        else
        {
            _logger.LogDebug("No tenant identifier found in request");
        }

        await _next(context);
    }

    private string? ResolveTenantIdentifier(HttpContext context)
    {
        // 1. Tentar resolver pelo header X-Tenant-Id
        if (context.Request.Headers.TryGetValue("X-Tenant-Id", out var tenantHeader))
        {
            var tenantId = tenantHeader.FirstOrDefault();
            if (!string.IsNullOrEmpty(tenantId))
            {
                _logger.LogDebug("Tenant resolved from header: {TenantId}", tenantId);
                return tenantId;
            }
        }

        // 2. Tentar resolver pelo subdomínio
        var host = context.Request.Host.Host;
        if (!string.IsNullOrEmpty(host))
        {
            var parts = host.Split('.');
            if (parts.Length > 2) // ex: tenant.example.com
            {
                var subdomain = parts[0];
                if (!string.IsNullOrEmpty(subdomain) && subdomain != "www")
                {
                    _logger.LogDebug("Tenant resolved from subdomain: {Subdomain}", subdomain);
                    return subdomain;
                }
            }
        }

        // 3. Tentar resolver pelo query parameter (para desenvolvimento)
        if (context.Request.Query.TryGetValue("tenant", out var tenantQuery))
        {
            var tenantId = tenantQuery.FirstOrDefault();
            if (!string.IsNullOrEmpty(tenantId))
            {
                _logger.LogDebug("Tenant resolved from query parameter: {TenantId}", tenantId);
                return tenantId;
            }
        }

        return null;
    }
}
