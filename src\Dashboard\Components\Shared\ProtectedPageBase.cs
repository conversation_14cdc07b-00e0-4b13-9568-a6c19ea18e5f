using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using System.Security.Claims;

namespace AssetView.Nx.Dashboard.Components.Shared;

/// <summary>
/// Classe base para páginas protegidas que requerem autenticação
/// </summary>
[Authorize]
public abstract class ProtectedPageBase : ComponentBase
{
    [Inject] protected AuthenticationStateProvider AuthenticationStateProvider { get; set; } = null!;
    [Inject] protected NavigationManager Navigation { get; set; } = null!;

    protected ClaimsPrincipal? CurrentUser { get; private set; }
    protected string? CurrentUserId { get; private set; }
    protected string? CurrentUserName { get; private set; }
    protected string? CurrentUserEmail { get; private set; }
    protected string? CurrentTenantId { get; private set; }
    protected List<string> UserRoles { get; private set; } = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadCurrentUserAsync();
        await base.OnInitializedAsync();
    }

    private async Task LoadCurrentUserAsync()
    {
        try
        {
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            CurrentUser = authState.User;

            if (CurrentUser?.Identity?.IsAuthenticated == true)
            {
                CurrentUserId = CurrentUser.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                CurrentUserName = CurrentUser.FindFirst(ClaimTypes.Name)?.Value;
                CurrentUserEmail = CurrentUser.FindFirst(ClaimTypes.Email)?.Value;
                CurrentTenantId = CurrentUser.FindFirst("TenantId")?.Value;
                UserRoles = CurrentUser.FindAll(ClaimTypes.Role).Select(c => c.Value).ToList();
            }
        }
        catch (Exception)
        {
            // Se houver erro ao carregar o usuário, redirecionar para login
            Navigation.NavigateTo("/login", true);
        }
    }

    /// <summary>
    /// Verifica se o usuário atual tem uma role específica
    /// </summary>
    protected bool HasRole(string role)
    {
        return UserRoles.Contains(role);
    }

    /// <summary>
    /// Verifica se o usuário atual tem qualquer uma das roles especificadas
    /// </summary>
    protected bool HasAnyRole(params string[] roles)
    {
        return roles.Any(role => UserRoles.Contains(role));
    }

    /// <summary>
    /// Verifica se o usuário atual é SuperAdmin
    /// </summary>
    protected bool IsSuperAdmin => HasRole("SuperAdmin");

    /// <summary>
    /// Verifica se o usuário atual é Admin (SuperAdmin ou Admin)
    /// </summary>
    protected bool IsAdmin => HasAnyRole("SuperAdmin", "Admin");

    /// <summary>
    /// Verifica se o usuário atual é Manager (SuperAdmin, Admin ou Manager)
    /// </summary>
    protected bool IsManager => HasAnyRole("SuperAdmin", "Admin", "Manager");
}
