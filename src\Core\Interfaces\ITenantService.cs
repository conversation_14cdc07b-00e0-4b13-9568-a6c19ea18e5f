using AssetView.Nx.Core.Entities;

namespace AssetView.Nx.Core.Interfaces;

/// <summary>
/// Interface para serviços de tenant
/// </summary>
public interface ITenantService
{
    /// <summary>
    /// Obtém o tenant atual
    /// </summary>
    Task<Tenant?> GetCurrentTenantAsync();

    /// <summary>
    /// Obtém o ID do tenant atual
    /// </summary>
    string? GetCurrentTenantId();

    /// <summary>
    /// Define o tenant atual
    /// </summary>
    void SetCurrentTenant(string tenantId);

    /// <summary>
    /// Obtém tenant por subdomínio
    /// </summary>
    Task<Tenant?> GetTenantBySubdomainAsync(string subdomain);

    /// <summary>
    /// Obtém tenant por ID
    /// </summary>
    Task<Tenant?> GetTenantByIdAsync(string tenantId);

    /// <summary>
    /// Verifica se o tenant está ativo
    /// </summary>
    Task<bool> IsTenantActiveAsync(string tenantId);

    /// <summary>
    /// Obtém a string de conexão do tenant
    /// </summary>
    Task<string> GetTenantConnectionStringAsync(string tenantId);
}
