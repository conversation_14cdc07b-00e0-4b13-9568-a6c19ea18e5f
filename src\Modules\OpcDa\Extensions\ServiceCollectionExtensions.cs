using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Routing;
using AssetView.Nx.Modules.OpcDa.Interfaces;
using AssetView.Nx.Modules.OpcDa.Services;
using AssetView.Nx.Modules.OpcDa.Hubs;
using AssetView.Nx.Modules.OpcDa.Models;

namespace AssetView.Nx.Modules.OpcDa.Extensions;

/// <summary>
/// Extensões para configuração dos serviços OPC DA
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adiciona os serviços OPC DA ao container de DI
    /// </summary>
    /// <param name="services">Collection de serviços</param>
    /// <param name="configuration">Configuração da aplicação</param>
    /// <returns>Collection de serviços</returns>
    public static IServiceCollection AddOpcDaServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Registrar serviços básicos
        services.AddSingleton<IOpcDaService, OpcDaService>();
        services.AddSingleton<IOpcDiscoveryService, OpcDiscoveryService>();
        services.AddHostedService<OpcSignalRService>();

        // Registrar serviços avançados
        services.AddScoped<IOpcTagNavigationService, OpcTagNavigationService>();
        services.AddScoped<IOpcGroupManagementService, OpcGroupManagementService>();
        // Registrar serviços de monitoramento como singleton e hosted service
        services.AddSingleton<OpcRealTimeMonitoringService>();
        services.AddSingleton<IOpcRealTimeMonitoringService>(provider =>
            provider.GetRequiredService<OpcRealTimeMonitoringService>());
        services.AddHostedService<OpcRealTimeMonitoringService>(provider =>
            provider.GetRequiredService<OpcRealTimeMonitoringService>());

        // Registrar serviços de cache como singleton e hosted service
        services.AddSingleton<OpcConnectionCacheService>();
        services.AddSingleton<IOpcConnectionCacheService>(provider =>
            provider.GetRequiredService<OpcConnectionCacheService>());
        services.AddHostedService<OpcConnectionCacheService>(provider =>
            provider.GetRequiredService<OpcConnectionCacheService>());
        services.AddScoped<IOpcTimeoutService, OpcTimeoutService>();
        services.AddScoped<IOpcRetryService, OpcRetryService>();
        services.AddSingleton<OpcErrorHandlingService>();
        services.AddSingleton<OpcRealBrowsingService>();
        services.AddSingleton<OpcConnectionPoolService>();
        services.AddSingleton<OpcPerformanceMetricsService>();

        // Configurar SignalR
        services.AddSignalR(options =>
        {
            options.EnableDetailedErrors = true;
            options.KeepAliveInterval = TimeSpan.FromSeconds(15);
            options.ClientTimeoutInterval = TimeSpan.FromSeconds(30);
        });

        // Configurar opções OPC
        services.Configure<OpcServerConfig>(configuration.GetSection("OpcServer"));
        services.Configure<OpcConnectionCacheOptions>(configuration.GetSection("OpcConnectionCache"));
        services.Configure<OpcTimeoutOptions>(configuration.GetSection("OpcTimeout"));
        services.Configure<OpcRetryOptions>(configuration.GetSection("OpcRetry"));
        services.Configure<OpcConnectionPoolConfig>(configuration.GetSection("OpcDa:ConnectionPool"));
        services.Configure<OpcMetricsConfig>(configuration.GetSection("OpcDa:Metrics"));

        return services;
    }

    /// <summary>
    /// Configura os endpoints SignalR para OPC
    /// </summary>
    /// <param name="app">Application builder</param>
    /// <returns>Application builder</returns>
    public static IApplicationBuilder UseOpcDaSignalR(this IApplicationBuilder app)
    {
        app.UseRouting();
        app.UseEndpoints(endpoints =>
        {
            endpoints.MapHub<OpcDataHub>("/opcHub");
        });

        return app;
    }
}
