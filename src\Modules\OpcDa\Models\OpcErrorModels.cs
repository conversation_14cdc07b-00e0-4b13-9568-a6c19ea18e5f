namespace AssetView.Nx.Modules.OpcDa.Models;

/// <summary>
/// Informações detalhadas sobre um erro OPC
/// </summary>
public class OpcErrorInfo
{
    /// <summary>
    /// Tipo do erro OPC
    /// </summary>
    public OpcErrorType ErrorType { get; set; }

    /// <summary>
    /// Código do erro (HRESULT ou código OPC)
    /// </summary>
    public int ErrorCode { get; set; }

    /// <summary>
    /// Mensagem descritiva do erro
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Contexto onde o erro ocorreu
    /// </summary>
    public string Context { get; set; } = string.Empty;

    /// <summary>
    /// ProgID do servidor OPC (se aplicável)
    /// </summary>
    public string? ServerProgId { get; set; }

    /// <summary>
    /// Timestamp do erro
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Severidade do erro
    /// </summary>
    public OpcErrorSeverity Severity { get; set; }

    /// <summary>
    /// Indica se o erro é recuperável
    /// </summary>
    public bool IsRecoverable { get; set; }

    /// <summary>
    /// Ação sugerida para resolver o erro
    /// </summary>
    public string? SuggestedAction { get; set; }

    /// <summary>
    /// Exceção original
    /// </summary>
    public Exception? Exception { get; set; }

    /// <summary>
    /// Informações adicionais sobre o erro
    /// </summary>
    public Dictionary<string, object> AdditionalInfo { get; set; } = new();
}

/// <summary>
/// Tipos de erro OPC
/// </summary>
public enum OpcErrorType
{
    /// <summary>
    /// Erro desconhecido
    /// </summary>
    Unknown,

    /// <summary>
    /// Falha na conexão
    /// </summary>
    ConnectionFailed,

    /// <summary>
    /// Servidor não disponível
    /// </summary>
    ServerNotAvailable,

    /// <summary>
    /// Timeout na operação
    /// </summary>
    Timeout,

    /// <summary>
    /// Erro COM/DCOM
    /// </summary>
    ComError,

    /// <summary>
    /// Falha na operação
    /// </summary>
    OperationFailed,

    /// <summary>
    /// Argumento inválido
    /// </summary>
    InvalidArgument,

    /// <summary>
    /// Falta de memória
    /// </summary>
    OutOfMemory,

    /// <summary>
    /// Interface não suportada
    /// </summary>
    InterfaceNotSupported,

    /// <summary>
    /// Acesso negado
    /// </summary>
    AccessDenied,

    /// <summary>
    /// Item não encontrado
    /// </summary>
    ItemNotFound,

    /// <summary>
    /// Grupo não encontrado
    /// </summary>
    GroupNotFound,

    /// <summary>
    /// Qualidade de dados ruim
    /// </summary>
    BadQuality,

    /// <summary>
    /// Erro de configuração
    /// </summary>
    ConfigurationError,

    /// <summary>
    /// Erro de autenticação
    /// </summary>
    AuthenticationError,

    /// <summary>
    /// Erro de rede
    /// </summary>
    NetworkError
}

/// <summary>
/// Severidade do erro OPC
/// </summary>
public enum OpcErrorSeverity
{
    /// <summary>
    /// Baixa - informativo
    /// </summary>
    Low,

    /// <summary>
    /// Média - aviso
    /// </summary>
    Medium,

    /// <summary>
    /// Alta - erro
    /// </summary>
    High,

    /// <summary>
    /// Crítica - erro crítico
    /// </summary>
    Critical
}

/// <summary>
/// Estratégia de recuperação de erro
/// </summary>
public class OpcErrorRecoveryStrategy
{
    /// <summary>
    /// Número máximo de tentativas
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// Delay inicial entre tentativas
    /// </summary>
    public TimeSpan InitialDelay { get; set; } = TimeSpan.FromSeconds(1);

    /// <summary>
    /// Multiplicador para backoff exponencial
    /// </summary>
    public double BackoffMultiplier { get; set; } = 2.0;

    /// <summary>
    /// Delay máximo entre tentativas
    /// </summary>
    public TimeSpan MaxDelay { get; set; } = TimeSpan.FromMinutes(1);

    /// <summary>
    /// Tipos de erro que devem ser recuperados
    /// </summary>
    public HashSet<OpcErrorType> RecoverableErrorTypes { get; set; } = new()
    {
        OpcErrorType.ConnectionFailed,
        OpcErrorType.ServerNotAvailable,
        OpcErrorType.Timeout,
        OpcErrorType.NetworkError,
        OpcErrorType.OperationFailed
    };

    /// <summary>
    /// Códigos de erro específicos que devem ser recuperados
    /// </summary>
    public HashSet<int> RecoverableErrorCodes { get; set; } = new();

    /// <summary>
    /// Ação personalizada a ser executada antes do retry
    /// </summary>
    public Func<OpcErrorInfo, Task>? PreRetryAction { get; set; }

    /// <summary>
    /// Ação personalizada a ser executada após falha definitiva
    /// </summary>
    public Func<OpcErrorInfo, Task>? OnFinalFailureAction { get; set; }
}

/// <summary>
/// Estatísticas de erro OPC
/// </summary>
public class OpcErrorStatistics
{
    /// <summary>
    /// Total de erros registrados
    /// </summary>
    public int TotalErrors { get; set; }

    /// <summary>
    /// Erros por tipo
    /// </summary>
    public Dictionary<OpcErrorType, int> ErrorsByType { get; set; } = new();

    /// <summary>
    /// Erros por severidade
    /// </summary>
    public Dictionary<OpcErrorSeverity, int> ErrorsBySeverity { get; set; } = new();

    /// <summary>
    /// Erros por servidor
    /// </summary>
    public Dictionary<string, int> ErrorsByServer { get; set; } = new();

    /// <summary>
    /// Último erro registrado
    /// </summary>
    public DateTime? LastErrorTime { get; set; }

    /// <summary>
    /// Taxa de erro (erros por hora)
    /// </summary>
    public double ErrorRate { get; set; }

    /// <summary>
    /// Período das estatísticas
    /// </summary>
    public TimeSpan Period { get; set; }

    /// <summary>
    /// Timestamp da geração das estatísticas
    /// </summary>
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Configuração de monitoramento de erros
/// </summary>
public class OpcErrorMonitoringConfig
{
    /// <summary>
    /// Habilitar monitoramento de erros
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Intervalo de limpeza de erros antigos
    /// </summary>
    public TimeSpan CleanupInterval { get; set; } = TimeSpan.FromHours(1);

    /// <summary>
    /// Idade máxima dos erros mantidos
    /// </summary>
    public TimeSpan MaxErrorAge { get; set; } = TimeSpan.FromDays(7);

    /// <summary>
    /// Limite de erros por servidor antes de alertar
    /// </summary>
    public int ErrorThresholdPerServer { get; set; } = 10;

    /// <summary>
    /// Período para calcular threshold
    /// </summary>
    public TimeSpan ThresholdPeriod { get; set; } = TimeSpan.FromMinutes(5);

    /// <summary>
    /// Habilitar alertas por email
    /// </summary>
    public bool EnableEmailAlerts { get; set; } = false;

    /// <summary>
    /// Endereços de email para alertas
    /// </summary>
    public List<string> AlertEmailAddresses { get; set; } = new();

    /// <summary>
    /// Habilitar logging detalhado de erros
    /// </summary>
    public bool EnableDetailedLogging { get; set; } = true;

    /// <summary>
    /// Nível mínimo de severidade para logging
    /// </summary>
    public OpcErrorSeverity MinLogSeverity { get; set; } = OpcErrorSeverity.Medium;
}
