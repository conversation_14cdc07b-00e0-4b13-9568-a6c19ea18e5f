using Microsoft.Extensions.Logging;
using Microsoft.Win32;
using System.Runtime.InteropServices;
using AssetView.Nx.Modules.OpcDa.Interfaces;
using AssetView.Nx.Modules.OpcDa.Models;
using Opc;
using OpcCom;

namespace AssetView.Nx.Modules.OpcDa.Services;

/// <summary>
/// Implementação do serviço de descoberta OPC
/// </summary>
public class OpcDiscoveryService : IOpcDiscoveryService
{
    private readonly ILogger<OpcDiscoveryService> _logger;
    private readonly IOpcDaService _opcService;
    private readonly IOpcGroupManagementService _groupManagementService;
    private OpcRealBrowsingService? _realBrowsingService;
    private OpcServerInfo? _currentServer;
    private readonly List<OpcTagInfo> _availableTags = new();
    private readonly Dictionary<string, List<OpcServerInfo>> _serverCache = new();
    private readonly object _cacheLock = new();

    public OpcDiscoveryService(ILogger<OpcDiscoveryService> logger, IOpcDaService opcService, IOpcGroupManagementService groupManagementService)
    {
        _logger = logger;
        _opcService = opcService;
        _groupManagementService = groupManagementService;
        _realBrowsingService = null; // Será injetado via propriedade se necessário
    }

    /// <summary>
    /// Define o serviço de browsing real (para evitar dependência circular)
    /// </summary>
    public void SetRealBrowsingService(OpcRealBrowsingService realBrowsingService)
    {
        _realBrowsingService = realBrowsingService;
    }

    public async Task<IEnumerable<OpcServerInfo>> DiscoverServersAsync(string host = "localhost", CancellationToken cancellationToken = default)
    {
        // Verificar cache primeiro
        lock (_cacheLock)
        {
            if (_serverCache.TryGetValue(host, out var cachedServers))
            {
                var cacheAge = DateTime.UtcNow - cachedServers.FirstOrDefault()?.LastChecked;
                if (cacheAge?.TotalMinutes < 5) // Cache válido por 5 minutos
                {
                    _logger.LogDebug("Retornando servidores do cache para host: {Host}", host);
                    return cachedServers;
                }
            }
        }

        return await RefreshServerDiscoveryAsync(host, cancellationToken);
    }

    public async Task<IEnumerable<OpcServerInfo>> RefreshServerDiscoveryAsync(string host = "localhost", CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Descobrindo servidores OPC no host: {Host}", host);

            var servers = new List<OpcServerInfo>();

            // Descobrir servidores OPC registrados no sistema (apenas para localhost)
            if (host.Equals("localhost", StringComparison.OrdinalIgnoreCase) ||
                host.Equals("127.0.0.1", StringComparison.OrdinalIgnoreCase) ||
                host.Equals(Environment.MachineName, StringComparison.OrdinalIgnoreCase))
            {
                var registeredServers = await DiscoverRegisteredOpcServersAsync(host);
                servers.AddRange(registeredServers);
            }

            // Adicionar servidores conhecidos que podem não estar no registro
            /*var knownServers = await DiscoverKnownOpcServersAsync(host);
            foreach (var knownServer in knownServers)
            {
                // Adicionar apenas se não foi encontrado no registro
                if (!servers.Any(s => s.ProgId.Equals(knownServer.ProgId, StringComparison.OrdinalIgnoreCase)))
                {
                    servers.Add(knownServer);
                }
            }*/

            // Marcar timestamp de verificação
            foreach (var server in servers)
            {
                server.LastChecked = DateTime.UtcNow;
            }

            // Atualizar cache
            lock (_cacheLock)
            {
                _serverCache[host] = servers;
            }

            _logger.LogInformation("Encontrados {Count} servidores OPC, {Available} disponíveis no host {Host}",
                servers.Count, servers.Count(s => s.IsAvailable), host);

            return servers.OrderBy(s => s.DisplayName).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao descobrir servidores OPC no host: {Host}", host);
            return Enumerable.Empty<OpcServerInfo>();
        }
    }

    public async Task<IEnumerable<OpcServerInfo>> DiscoverServersInNetworkAsync(IEnumerable<string> hosts, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Descobrindo servidores OPC em {HostCount} hosts da rede", hosts.Count());

            var allServers = new List<OpcServerInfo>();
            var tasks = new List<Task<IEnumerable<OpcServerInfo>>>();

            // Executar descoberta em paralelo para todos os hosts
            foreach (var host in hosts)
            {
                if (cancellationToken.IsCancellationRequested)
                    break;

                tasks.Add(DiscoverServersAsync(host, cancellationToken));
            }

            // Aguardar todas as descobertas
            var results = await Task.WhenAll(tasks);

            // Consolidar resultados
            foreach (var serverList in results)
            {
                allServers.AddRange(serverList);
            }

            _logger.LogInformation("Descoberta de rede concluída: {TotalServers} servidores encontrados em {HostCount} hosts",
                allServers.Count, hosts.Count());

            return allServers.OrderBy(s => s.Host).ThenBy(s => s.DisplayName).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao descobrir servidores OPC na rede");
            return Enumerable.Empty<OpcServerInfo>();
        }
    }

    public async Task<bool> IsServerAvailableAsync(string progId, string host = "localhost", CancellationToken cancellationToken = default)
    {
        return await CheckServerAvailability(progId);
    }

    /// <summary>
    /// Função chamada quando pede para conectar a um OPC Server.
    /// </summary>
    /// <param name="serverInfo"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns> <summary>
    /// </summary>
    /// <param name="serverInfo"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<bool> ConnectToServerAsync(OpcServerInfo serverInfo, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("=== INICIANDO CONEXÃO OPC ===");
            _logger.LogInformation("Conectando ao servidor OPC: {ProgId} no host: {Host}", serverInfo.ProgId, serverInfo.Host);

            // Desconectar servidor anterior se existir
            if (_currentServer != null && _currentServer.IsConnected)
            {
                await DisconnectFromServerAsync(cancellationToken);
            }

            // Tentar conexão usando o serviço OPC existente
            var config = new OpcServerConfig
            {
                ProgId = serverInfo.ProgId,
                ServerName = serverInfo.DisplayName,
                Host = serverInfo.Host,
                Groups = new List<OpcGroup>() // Sem grupos iniciais
            };

            var success = await _opcService.ConnectAsync(config, cancellationToken);

            if (success)
            {
                _currentServer = serverInfo;
                _currentServer.IsConnected = true;

                // Carregar tags disponíveis
                //await LoadAvailableTagsAsync();

                // Iniciar monitoramento automático para manter o serviço ativo
                _logger.LogInformation("Iniciando monitoramento automático do OpcDaService");
                await _opcService.StartMonitoringAsync();

                _logger.LogInformation("Conectado com sucesso ao servidor OPC: {DisplayName}", serverInfo.DisplayName);
                return true;
            }
            else
            {
                // Se falhou, tentar verificação básica de disponibilidade
                _logger.LogWarning("Conexão via OpcService falhou, tentando verificação básica");

                var isAvailable = await CheckServerAvailability(serverInfo.ProgId);
                if (isAvailable)
                {
                    // Tentar conectar novamente com configuração mais simples
                    _logger.LogInformation("Tentando reconectar OpcDaService com configuração simplificada: {ProgId}", serverInfo.ProgId);

                    var simpleConfig = new OpcServerConfig
                    {
                        ProgId = serverInfo.ProgId,
                        Host = serverInfo.Host,
                        ConnectionTimeout = 10000, // Timeout menor
                        MaxReconnectAttempts = 1,
                        ReconnectInterval = 1000,
                        Groups = new List<OpcGroup>()
                    };

                    var retrySuccess = await _opcService.ConnectAsync(simpleConfig, cancellationToken);

                    _currentServer = serverInfo;
                    _currentServer.IsConnected = true;

                    // Carregar tags padrão
                    //await LoadAvailableTagsAsync();

                    if (retrySuccess)
                    {
                        _logger.LogInformation("Servidor OPC {DisplayName} conectado com sucesso na segunda tentativa", serverInfo.DisplayName);
                    }
                    else
                    {
                        _logger.LogWarning("Servidor OPC {DisplayName} marcado como conectado (modo simulado - OpcDaService não conectou)", serverInfo.DisplayName);
                    }
                    return true;
                }

                _logger.LogError("Servidor OPC não está disponível: {ProgId}", serverInfo.ProgId);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao conectar ao servidor OPC: {ProgId}. Detalhes: {Message}",
                serverInfo.ProgId, ex.Message);

            // Garantir limpeza em caso de erro
            if (_currentServer != null)
            {
                _currentServer.IsConnected = false;
            }

            return false;
        }
    }

    public async Task DisconnectFromServerAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            if (_currentServer != null)
            {
                _logger.LogInformation("Desconectando do servidor: {DisplayName}", _currentServer.DisplayName);

                // Tentar desconectar via serviço OPC
                try
                {
                    await _opcService.DisconnectAsync(cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Erro ao desconectar via OpcService");
                }

                // Limpar estado local
                if (_currentServer != null)
                {
                    _currentServer.IsConnected = false;
                }
                _currentServer = null;
                _availableTags.Clear();

                _logger.LogInformation("Servidor OPC desconectado com sucesso");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao desconectar do servidor OPC");
        }
    }

    public async Task<IEnumerable<OpcTagInfo>> BrowseTagsAsync(string path = "", CancellationToken cancellationToken = default)
    {
        try
        {
            if (_currentServer == null)
            {
                _logger.LogWarning("Nenhum servidor conectado para navegar tags");
                return Enumerable.Empty<OpcTagInfo>();
            }

            _logger.LogDebug("Navegando tags no caminho: {Path}", string.IsNullOrEmpty(path) ? "raiz" : path);

            // Filtrar tags baseado no caminho
            var tags = _availableTags.AsEnumerable();
            
            if (!string.IsNullOrEmpty(path))
            {
                tags = tags.Where(t => t.Path?.StartsWith(path, StringComparison.OrdinalIgnoreCase) == true);
            }

            return tags.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao navegar tags no caminho: {Path}", path);
            return Enumerable.Empty<OpcTagInfo>();
        }
    }

    public async Task<IEnumerable<OpcTagInfo>> SearchTagsAsync(string searchPattern, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_currentServer == null)
            {
                return Enumerable.Empty<OpcTagInfo>();
            }

            _logger.LogDebug("Buscando tags com padrão: {Pattern}", searchPattern);

            var tags = _availableTags.Where(t => 
                t.TagName.Contains(searchPattern, StringComparison.OrdinalIgnoreCase) ||
                t.DisplayName.Contains(searchPattern, StringComparison.OrdinalIgnoreCase) ||
                (t.Description?.Contains(searchPattern, StringComparison.OrdinalIgnoreCase) == true)
            ).ToList();

            _logger.LogDebug("Encontrados {Count} tags correspondentes ao padrão", tags.Count);
            return tags;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar tags com padrão: {Pattern}", searchPattern);
            return Enumerable.Empty<OpcTagInfo>();
        }
    }

    public async Task<bool> AddTagToMonitoringAsync(AddTagRequest request, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("🚀 Adicionando tag REAL {TagName} ao grupo {GroupName}", request.TagName, request.GroupName);

            // Verificar se temos um servidor conectado no Discovery
            if (_currentServer == null || !_currentServer.IsConnected)
            {
                _logger.LogError("❌ Nenhum servidor OPC conectado no Discovery Service");
                return false;
            }

            // Verificar se o OpcDaService está conectado
            var connectionStatus = _opcService.GetConnectionStatus();
            if (connectionStatus != OpcConnectionStatus.Connected)
            {
                _logger.LogWarning("⚠️ OpcDaService não está conectado. Status: {Status}. Tentando conectar...", connectionStatus);

                // Tentar conectar o OpcDaService usando as informações do servidor atual
                var config = new OpcServerConfig
                {
                    ProgId = _currentServer.ProgId,
                    ServerName = _currentServer.DisplayName,
                    Host = _currentServer.Host,
                    Groups = new List<OpcGroup>()
                };

                var connected = await _opcService.ConnectAsync(config, cancellationToken);
                if (!connected)
                {
                    _logger.LogError("❌ Falha ao conectar OpcDaService ao servidor {ProgId}@{Host}",
                        _currentServer.ProgId, _currentServer.Host);
                    return false;
                }

                _logger.LogInformation("✅ OpcDaService conectado com sucesso!");
            }

            // Criar item OPC REAL
            var opcItem = new OpcDataItem
            {
                Id = Guid.NewGuid().ToString(),
                TagName = request.TagName,
                Description = request.Description ?? request.TagName,
                Unit = request.Unit,
                MinValue = request.MinValue,
                MaxValue = request.MaxValue,
                IsActive = true,
                GroupName = request.GroupName
            };

            bool success = false;

            // Verificar se o grupo já existe
            if (_opcService.GroupExists(request.GroupName))
            {
                _logger.LogInformation("📁 Grupo {GroupName} já existe, adicionando item ao grupo existente", request.GroupName);

                // Adicionar item ao grupo existente
                success = await _opcService.AddItemToGroupAsync(request.GroupName, opcItem, cancellationToken);
            }
            else
            {
                _logger.LogInformation("🆕 Criando novo grupo {GroupName} com o item", request.GroupName);

                // Criar novo grupo com o item
                var opcGroup = new OpcGroup
                {
                    Name = request.GroupName,
                    IsActive = true,
                    UpdateRate = request.UpdateRate,
                    Items = new List<OpcDataItem> { opcItem }
                };

                success = await _opcService.AddGroupAsync(opcGroup, cancellationToken);
            }

            if (success)
            {
                _logger.LogInformation("✅ Tag {TagName} adicionado com SUCESSO ao monitoramento REAL no grupo {GroupName}",
                    request.TagName, request.GroupName);

                // Iniciar monitoramento se não estiver ativo
                try
                {
                    await _opcService.StartMonitoringAsync(cancellationToken);
                    _logger.LogDebug("🔄 Monitoramento iniciado/verificado");
                }
                catch (Exception monitorEx)
                {
                    _logger.LogWarning(monitorEx, "⚠️ Erro ao iniciar monitoramento, mas tag foi adicionado");
                }

                return true;
            }
            else
            {
                _logger.LogError("❌ Falha ao adicionar tag {TagName} ao grupo {GroupName}", request.TagName, request.GroupName);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Erro ao adicionar tag {TagName} ao monitoramento REAL", request.TagName);
            return false;
        }
    }

    public async Task<IEnumerable<OpcTagInfo>> GetAvailableTagsAsync()
    {
        return await Task.FromResult(_availableTags.AsEnumerable());
    }

    public async Task<bool> AddAvailableTagAsync(OpcTagInfo tagInfo)
    {
        try
        {
            // Verificar se o tag já existe
            if (!_availableTags.Any(t => t.TagName == tagInfo.TagName))
            {
                _availableTags.Add(tagInfo);
                _logger.LogDebug("Tag {TagName} adicionado à lista de tags disponíveis", tagInfo.TagName);
                return true;
            }

            _logger.LogDebug("Tag {TagName} já existe na lista de tags disponíveis", tagInfo.TagName);
            return true; // Retorna true mesmo se já existir
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao adicionar tag {TagName} à lista de disponíveis", tagInfo.TagName);
            return false;
        }
    }

    public async Task<bool> RemoveTagFromMonitoringAsync(string tagName, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("🗑️ Removendo tag REAL {TagName} do monitoramento", tagName);

            // Verificar se o OpcDaService está conectado
            var connectionStatus = _opcService.GetConnectionStatus();
            if (connectionStatus != OpcConnectionStatus.Connected)
            {
                _logger.LogWarning("❌ OpcDaService não está conectado. Status: {Status}", connectionStatus);
                return false;
            }

            // Encontrar a tag nos tags disponíveis
            var tag = _availableTags.FirstOrDefault(t => t.TagName == tagName);
            if (tag == null)
            {
                _logger.LogWarning("❌ Tag {TagName} não encontrado na lista de tags disponíveis", tagName);
                return false;
            }

            // Verificar se a tag está em um grupo
            if (string.IsNullOrEmpty(tag.GroupName))
            {
                _logger.LogWarning("❌ Tag {TagName} não está associado a nenhum grupo", tagName);
                return false;
            }

            // Obter o servidor atual
            var serverInfo = GetCurrentServerInfo();
            if (serverInfo == null)
            {
                _logger.LogWarning("❌ Nenhum servidor OPC conectado atualmente");
                return false;
            }

            // Remover a tag do grupo usando o serviço de gerenciamento de grupos
            await _groupManagementService.RemoveTagsFromGroupAsync(
                serverInfo.ProgId,
                tag.GroupName,
                new List<string> { tagName },
                cancellationToken);

            // Atualizar o status da tag
            tag.IsMonitored = false;
            tag.GroupName = null;

            _logger.LogInformation("✅ Tag {TagName} removido do monitoramento com sucesso", tagName);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Erro ao remover tag {TagName} do monitoramento", tagName);
            return false;
        }
    }

    public OpcServerInfo? GetCurrentServerInfo()
    {
        return _currentServer;
    }

    public async Task<IEnumerable<string>> GetAvailableGroupsAsync()
    {
        // Retornar grupos padrão + grupos existentes
        var defaultGroups = new[] { "ProcessData", "AlarmData", "MotorData", "QualityData" };
        return defaultGroups;
    }

    public async Task<bool> CreateGroupAsync(string groupName, int updateRate = 1000, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Criando grupo {GroupName} com taxa de atualização {UpdateRate}ms", groupName, updateRate);

            var group = new OpcGroup
            {
                Name = groupName,
                IsActive = true,
                UpdateRate = updateRate,
                Items = new List<OpcDataItem>()
            };

            // Em implementação real, adicionaria o grupo ao serviço OPC
            // await _opcService.AddGroupAsync(group, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao criar grupo {GroupName}", groupName);
            return false;
        }
    }

    /// <summary>
    /// Descobre servidores OPC usando OpcNetApi.Com (biblioteca .NET para OPC)
    /// </summary>
    private async Task<List<OpcServerInfo>> DiscoverRegisteredOpcServersAsync(string host)
    {
        var servers = new List<OpcServerInfo>();

        try
        {
            _logger.LogDebug("Descobrindo servidores OPC usando OpcNetApi.Com para host: {Host}", host);

            await Task.Run(() =>
            {
                try
                {
                    // Usar OpcNetApi.Com para descoberta de servidores
                    var discovery = new OpcCom.ServerEnumerator();

                    // Descobrir servidores OPC DA no host especificado
                    var opcServers = discovery.GetAvailableServers(Specification.COM_DA_20, host, null);

                    if (opcServers != null && opcServers.Length > 0)
                    {
                        foreach (var opcServer in opcServers)
                        {
                            try
                            {
                                var serverInfo = CreateServerInfoFromOpcNetApi(opcServer, host);
                                if (serverInfo != null)
                                {
                                    servers.Add(serverInfo);
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogDebug(ex, "Erro ao processar servidor OPC: {Name}", opcServer.Name);
                            }
                        }
                    }

                    // Tentar também OPC DA 1.0 se não encontrou servidores 2.0
                    if (servers.Count == 0)
                    {
                        try
                        {
                            var opcServers10 = discovery.GetAvailableServers(Specification.COM_DA_10, host, null);
                            if (opcServers10 != null && opcServers10.Length > 0)
                            {
                                foreach (var opcServer in opcServers10)
                                {
                                    try
                                    {
                                        var serverInfo = CreateServerInfoFromOpcNetApi(opcServer, host);
                                        if (serverInfo != null)
                                        {
                                            servers.Add(serverInfo);
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger.LogDebug(ex, "Erro ao processar servidor OPC 1.0: {Name}", opcServer.Name);
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogDebug(ex, "Erro ao buscar servidores OPC DA 1.0");
                        }
                    }
                }
                catch (COMException comEx)
                {
                    _logger.LogWarning("Erro COM ao descobrir servidores OPC: {Error} (Código: 0x{HResult:X8})",
                        comEx.Message, comEx.HResult);

                    // Fallback para descoberta via registro se OpcNetApi falhar
                    var registryServers = DiscoverViaRegistryFallback(host);
                    servers.AddRange(registryServers);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Erro ao usar OpcNetApi.Com, tentando fallback via registro");

                    // Fallback para descoberta via registro
                    var registryServers = DiscoverViaRegistryFallback(host);
                    servers.AddRange(registryServers);
                }
            });

            _logger.LogDebug("Encontrados {Count} servidores OPC via OpcNetApi.Com", servers.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao descobrir servidores OPC via OpcNetApi.Com");
        }

        return servers;
    }

    /// <summary>
    /// Descobre servidores OPC conhecidos (fallback para servidores comuns)
    /// </summary>
    private async Task<List<OpcServerInfo>> DiscoverKnownOpcServersAsync(string host)
    {
        var knownServers = new[]
        {
            new { ProgId = "Matrikon.OPC.Simulation.1", DisplayName = "Matrikon OPC Server for Simulation", Vendor = "Matrikon", Description = "Servidor de simulação OPC para desenvolvimento e testes" },
            new { ProgId = "KEPware.KEPServerEx.V6", DisplayName = "KEPServerEX", Vendor = "PTC", Description = "Servidor OPC industrial da PTC" },
            new { ProgId = "OPC.SimaticNet", DisplayName = "SIMATIC NET OPC Server", Vendor = "Siemens", Description = "Servidor OPC da Siemens para equipamentos SIMATIC" },
            new { ProgId = "RSLinx.OPC.Server", DisplayName = "RSLinx OPC Server", Vendor = "Rockwell Automation", Description = "Servidor OPC da Rockwell Automation" },
            new { ProgId = "Wonderware.SuiteLink", DisplayName = "Wonderware SuiteLink", Vendor = "Wonderware", Description = "Servidor OPC da Wonderware" },
            new { ProgId = "National Instruments.OPCServer", DisplayName = "NI OPC Server", Vendor = "National Instruments", Description = "Servidor OPC da National Instruments" },
            new { ProgId = "Schneider-Aut.OFS.2", DisplayName = "OFS (OPC Factory Server)", Vendor = "Schneider Electric", Description = "Servidor OPC da Schneider Electric" },
            new { ProgId = "Intellution.OPCServer", DisplayName = "Intellution OPC Server", Vendor = "Intellution", Description = "Servidor OPC da Intellution" }
        };

        var servers = new List<OpcServerInfo>();

        foreach (var known in knownServers)
        {
            try
            {
                var isAvailable = await CheckServerAvailability(known.ProgId);

                servers.Add(new OpcServerInfo
                {
                    ProgId = known.ProgId,
                    DisplayName = known.DisplayName,
                    Description = known.Description,
                    Vendor = known.Vendor,
                    Version = "Unknown",
                    Host = host,
                    IsAvailable = isAvailable
                });
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Erro ao verificar servidor conhecido: {ProgId}", known.ProgId);
            }
        }

        return servers;
    }

    /// <summary>
    /// Busca ProgIDs relacionados a OPC no registro
    /// </summary>
    private List<string> FindOpcProgIds(RegistryKey classesRoot)
    {
        var progIds = new List<string>();

        try
        {
            var subKeyNames = classesRoot.GetSubKeyNames();

            foreach (var keyName in subKeyNames)
            {
                // Buscar por chaves que contenham "OPC" no nome
                if (keyName.Contains("OPC", StringComparison.OrdinalIgnoreCase) ||
                    keyName.Contains("Matrikon", StringComparison.OrdinalIgnoreCase) ||
                    keyName.Contains("KEPware", StringComparison.OrdinalIgnoreCase) ||
                    keyName.Contains("Simatic", StringComparison.OrdinalIgnoreCase) ||
                    keyName.Contains("RSLinx", StringComparison.OrdinalIgnoreCase))
                {
                    try
                    {
                        using var subKey = classesRoot.OpenSubKey(keyName);
                        if (subKey != null)
                        {
                            // Verificar se tem CLSID (indicando que é um componente COM)
                            using var clsidKey = subKey.OpenSubKey("CLSID");
                            if (clsidKey != null)
                            {
                                progIds.Add(keyName);
                                _logger.LogDebug("Encontrado ProgID OPC: {ProgId}", keyName);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug(ex, "Erro ao verificar chave do registro: {KeyName}", keyName);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao enumerar chaves do registro");
        }

        return progIds;
    }

    /// <summary>
    /// Cria informações do servidor a partir dos dados do OpcNetApi.Com
    /// </summary>
    private OpcServerInfo? CreateServerInfoFromOpcNetApi(Opc.Server opcServer, string host)
    {
        try
        {
            var displayName = !string.IsNullOrEmpty(opcServer.Name) ? opcServer.Name : "Servidor OPC";
            var description = "Servidor OPC DA descoberto via OpcNetApi.Com";
            var progId = opcServer.Name; // OpcNetApi usa Name como identificador
            var vendor = DetermineVendorFromProgId(progId);
            var version = "Unknown";

            // Tentar extrair ProgID se disponível
            if (opcServer.Url != null)
            {
                try
                {
                    // URL format: opcda://host/progid
                    var urlParts = opcServer.Url.ToString().Split('/');
                    if (urlParts.Length > 2)
                    {
                        progId = urlParts[^1]; // Último segmento
                        vendor = DetermineVendorFromProgId(progId);
                        version = ExtractVersionFromProgId(progId);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "Erro ao extrair ProgID da URL: {Url}", opcServer.Url);
                }
            }

            // Verificar disponibilidade
            var isAvailable = CheckServerAvailabilityOpcNetApi(opcServer);

            return new OpcServerInfo
            {
                ProgId = progId,
                DisplayName = displayName,
                Description = description,
                Vendor = vendor,
                Version = version,
                Host = host,
                IsAvailable = isAvailable
            };
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Erro ao criar informações do servidor OpcNetApi: {Name}", opcServer.Name);
            return null;
        }
    }

    /// <summary>
    /// Verifica disponibilidade do servidor usando OpcNetApi
    /// </summary>
    private static bool CheckServerAvailabilityOpcNetApi(Opc.Server opcServer)
    {
        try
        {
            // Para OpcNetApi, assumir que se foi descoberto, está disponível
            // A verificação real seria muito custosa aqui
            return opcServer.Url != null;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Cria informações do servidor a partir dos dados do IOPCServerList2 (método original)
    /// </summary>
    private OpcServerInfo? CreateServerInfoFromOpcList(string progId, string userType, string verIndProgId, string host)
    {
        try
        {
            var displayName = !string.IsNullOrEmpty(userType) ? userType : progId;
            var description = "Servidor OPC DA descoberto via IOPCServerList2";
            var vendor = DetermineVendorFromProgId(progId);
            var version = ExtractVersionFromProgId(verIndProgId ?? progId);

            // Verificar disponibilidade
            var isAvailable = CheckServerAvailability(progId).Result;

            return new OpcServerInfo
            {
                ProgId = progId,
                DisplayName = displayName,
                Description = description,
                Vendor = vendor,
                Version = version,
                Host = host,
                IsAvailable = isAvailable
            };
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Erro ao criar informações do servidor para ProgID: {ProgId}", progId);
            return null;
        }
    }

    /// <summary>
    /// Fallback para descoberta via registro quando IOPCServerList2 falha
    /// </summary>
    private List<OpcServerInfo> DiscoverViaRegistryFallback(string host)
    {
        var servers = new List<OpcServerInfo>();

        try
        {
            _logger.LogDebug("Usando fallback de descoberta via registro para host: {Host}", host);

            using var classesRoot = Registry.ClassesRoot;
            var progIds = FindOpcProgIds(classesRoot);

            foreach (var progId in progIds)
            {
                try
                {
                    var serverInfo = CreateServerInfoFromProgId(progId, host);
                    if (serverInfo != null)
                    {
                        servers.Add(serverInfo);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "Erro ao processar ProgID no fallback: {ProgId}", progId);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro no fallback de descoberta via registro");
        }

        return servers;
    }

    /// <summary>
    /// Extrai versão do ProgID
    /// </summary>
    private static string ExtractVersionFromProgId(string progId)
    {
        if (string.IsNullOrEmpty(progId))
            return "Unknown";

        // Tentar extrair versão do final do ProgID (ex: "Server.1" -> "1")
        var parts = progId.Split('.');
        if (parts.Length > 1)
        {
            var lastPart = parts[^1];
            if (double.TryParse(lastPart, out _))
            {
                return lastPart;
            }
        }

        return "Unknown";
    }

    /// <summary>
    /// Cria informações do servidor a partir do ProgID (método original para fallback)
    /// </summary>
    private OpcServerInfo? CreateServerInfoFromProgId(string progId, string host)
    {
        try
        {
            using var classesRoot = Registry.ClassesRoot;
            using var progIdKey = classesRoot.OpenSubKey(progId);

            if (progIdKey == null)
                return null;

            var displayName = progIdKey.GetValue("")?.ToString() ?? progId;
            var description = "Servidor OPC encontrado no registro do sistema";
            var vendor = "Unknown";
            var version = "Unknown";

            // Tentar extrair informações adicionais
            try
            {
                using var clsidKey = progIdKey.OpenSubKey("CLSID");
                if (clsidKey != null)
                {
                    var clsid = clsidKey.GetValue("")?.ToString();
                    if (!string.IsNullOrEmpty(clsid))
                    {
                        using var clsidRoot = Registry.ClassesRoot.OpenSubKey($"CLSID\\{clsid}");
                        if (clsidRoot != null)
                        {
                            var clsidName = clsidRoot.GetValue("")?.ToString();
                            if (!string.IsNullOrEmpty(clsidName))
                            {
                                displayName = clsidName;
                            }

                            // Tentar obter informações de versão
                            using var versionKey = clsidRoot.OpenSubKey("Version");
                            if (versionKey != null)
                            {
                                version = versionKey.GetValue("")?.ToString() ?? version;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Erro ao obter informações detalhadas do ProgID: {ProgId}", progId);
            }

            // Determinar vendor baseado no ProgID
            vendor = DetermineVendorFromProgId(progId);

            var isAvailable = CheckServerAvailability(progId).Result;

            return new OpcServerInfo
            {
                ProgId = progId,
                DisplayName = displayName,
                Description = description,
                Vendor = vendor,
                Version = version,
                Host = host,
                IsAvailable = isAvailable
            };
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Erro ao criar informações do servidor para ProgID: {ProgId}", progId);
            return null;
        }
    }

    /// <summary>
    /// Determina o fornecedor baseado no ProgID
    /// </summary>
    private static string DetermineVendorFromProgId(string progId)
    {
        var progIdLower = progId.ToLowerInvariant();

        if (progIdLower.Contains("matrikon")) return "Matrikon";
        if (progIdLower.Contains("kepware")) return "PTC (KEPware)";
        if (progIdLower.Contains("simatic") || progIdLower.Contains("siemens")) return "Siemens";
        if (progIdLower.Contains("rslinx") || progIdLower.Contains("rockwell")) return "Rockwell Automation";
        if (progIdLower.Contains("wonderware")) return "Wonderware";
        if (progIdLower.Contains("schneider")) return "Schneider Electric";
        if (progIdLower.Contains("intellution")) return "Intellution";
        if (progIdLower.Contains("national") || progIdLower.Contains("ni.")) return "National Instruments";
        if (progIdLower.Contains("ge.") || progIdLower.Contains("gefanuc")) return "GE Fanuc";
        if (progIdLower.Contains("honeywell")) return "Honeywell";

        return "Unknown";
    }

    /// <summary>
    /// Cria URL do servidor OPC para conexão
    /// </summary>
    private static Opc.URL CreateOpcServerUrl(OpcServerInfo serverInfo)
    {
        try
        {
            // Formato da URL OPC: opcda://host/progid
            var host = serverInfo.Host;
            if (host.Equals("localhost", StringComparison.OrdinalIgnoreCase))
            {
                host = Environment.MachineName;
            }

            var urlString = $"opcda://{host}/{serverInfo.ProgId}";
            return new Opc.URL(urlString);
        }
        catch (Exception)
        {
            // Fallback para formato simples
            return new Opc.URL($"opcda://{serverInfo.Host}/{serverInfo.ProgId}");
        }
    }

    /// <summary>
    /// Verifica se um servidor OPC está disponível
    /// </summary>
    private async Task<bool> CheckServerAvailability(string progId)
    {
        try
        {
            return await Task.Run(() =>
            {
                try
                {
                    // Tentar obter o tipo COM do ProgID
                    var serverType = System.Type.GetTypeFromProgID(progId);
                    if (serverType == null)
                        return false;

                    // Tentar criar uma instância do servidor
                    var serverInstance = Activator.CreateInstance(serverType);
                    if (serverInstance != null)
                    {
                        // Liberar a instância imediatamente
                        if (Marshal.IsComObject(serverInstance))
                        {
                            Marshal.ReleaseComObject(serverInstance);
                        }
                        return true;
                    }

                    return false;
                }
                catch (COMException comEx)
                {
                    // Servidor registrado mas não disponível
                    _logger.LogDebug("Servidor {ProgId} registrado mas não disponível: {Error}", progId, comEx.Message);
                    return false;
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "Erro ao verificar disponibilidade do servidor: {ProgId}", progId);
                    return false;
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "Erro na verificação assíncrona do servidor: {ProgId}", progId);
            return false;
        }
    }

    private async Task LoadAvailableTagsAsync()
    {
        try
        {
            _availableTags.Clear();

            if (_currentServer != null)
            {
                _logger.LogInformation("=== CARREGANDO TAGS DO SERVIDOR ===");
                _logger.LogInformation("Carregando tags reais do servidor: {ProgId}", _currentServer.ProgId);

                // Tentar usar browsing real primeiro
                if (_realBrowsingService != null)
                {
                    _logger.LogInformation("RealBrowsingService disponível, iniciando browsing real");
                    try
                    {
                        var realTags = await _realBrowsingService.BrowseRealTagsAsync(
                            _currentServer.ProgId,
                            _currentServer.Host,
                            "", // Root path
                            CancellationToken.None);

                    foreach (var tag in realTags)
                    {
                        _availableTags.Add(new OpcTagInfo
                        {
                            TagName = tag.ItemId ?? tag.Name,
                            DisplayName = tag.Name,
                            DataType = tag.DataType,
                            Path = tag.ParentPath ?? "",
                            AccessRights = tag.AccessRights,
                            Description = tag.Description
                        });
                    }

                    _logger.LogInformation("Carregados {Count} tags reais via browsing", _availableTags.Count);
                }
                catch (Exception browsingEx)
                {
                    _logger.LogWarning(browsingEx, "Browsing real falhou, usando tags padrão");

                    // Fallback para tags padrão
                    LoadDefaultTagsForServer();
                }
            }
            else
            {
                _logger.LogWarning("OpcRealBrowsingService não disponível, usando tags padrão");
                LoadDefaultTagsForServer();
            }
            } // Fecha o bloco if (_currentServer != null)

            if (_availableTags.Count == 0)
            {
                _logger.LogWarning("Nenhum tag carregado, usando tags padrão");
                LoadDefaultTagsForServer();
            }

            _logger.LogInformation("Total de {Count} tags disponíveis", _availableTags.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao carregar tags disponíveis");

            // Fallback final
            LoadDefaultTagsForServer();
        }
    }

    /// <summary>
    /// Converte string de access rights para enum
    /// </summary>
    private OpcAccessRights ParseAccessRights(string accessRights)
    {
        if (string.IsNullOrEmpty(accessRights))
            return OpcAccessRights.Read;

        return accessRights.ToLowerInvariant() switch
        {
            "read" => OpcAccessRights.Read,
            "write" => OpcAccessRights.Write,
            "readwrite" or "read/write" => OpcAccessRights.ReadWrite,
            _ => OpcAccessRights.Read
        };
    }

    /// <summary>
    /// Carrega tags padrão quando navegação real falha
    /// </summary>
    private void LoadDefaultTagsForServer()
    {
        /*if (_currentServer?.ProgId == "Matrikon.OPC.Simulation.1")
        {
            // Carregar tags do Matrikon Simulation Server
            LoadMatrikonSimulationTags();
        }
        else
        {
            // Carregar tags genéricos para outros servidores
            LoadGenericTags();
        }*/
    }

    private void LoadMatrikonSimulationTags()
    {
        var tags = new[]
        {
            // Random tags
            new OpcTagInfo { TagName = "Random.Boolean", DisplayName = "Random Boolean", DataType = "Boolean", Path = "Random", AccessRights = OpcAccessRights.Read },
            new OpcTagInfo { TagName = "Random.Int1", DisplayName = "Random Int1", DataType = "Byte", Path = "Random", AccessRights = OpcAccessRights.Read },
            new OpcTagInfo { TagName = "Random.Int2", DisplayName = "Random Int2", DataType = "Int16", Path = "Random", AccessRights = OpcAccessRights.Read },
            new OpcTagInfo { TagName = "Random.Int4", DisplayName = "Random Int4", DataType = "Int32", Path = "Random", AccessRights = OpcAccessRights.Read },
            new OpcTagInfo { TagName = "Random.Real4", DisplayName = "Random Real4", DataType = "Single", Path = "Random", AccessRights = OpcAccessRights.Read },
            new OpcTagInfo { TagName = "Random.Real8", DisplayName = "Random Real8", DataType = "Double", Path = "Random", AccessRights = OpcAccessRights.Read },
            new OpcTagInfo { TagName = "Random.String", DisplayName = "Random String", DataType = "String", Path = "Random", AccessRights = OpcAccessRights.Read },

            // Triangle Waves
            new OpcTagInfo { TagName = "Triangle Waves.Int1", DisplayName = "Triangle Wave Int1", DataType = "Byte", Path = "Triangle Waves", AccessRights = OpcAccessRights.Read },
            new OpcTagInfo { TagName = "Triangle Waves.Int2", DisplayName = "Triangle Wave Int2", DataType = "Int16", Path = "Triangle Waves", AccessRights = OpcAccessRights.Read },
            new OpcTagInfo { TagName = "Triangle Waves.Int4", DisplayName = "Triangle Wave Int4", DataType = "Int32", Path = "Triangle Waves", AccessRights = OpcAccessRights.Read },
            new OpcTagInfo { TagName = "Triangle Waves.Real4", DisplayName = "Triangle Wave Real4", DataType = "Single", Path = "Triangle Waves", AccessRights = OpcAccessRights.Read },
            new OpcTagInfo { TagName = "Triangle Waves.Real8", DisplayName = "Triangle Wave Real8", DataType = "Double", Path = "Triangle Waves", AccessRights = OpcAccessRights.Read },

            // Saw-toothed Waves
            new OpcTagInfo { TagName = "Saw-toothed Waves.Int1", DisplayName = "Saw Wave Int1", DataType = "Byte", Path = "Saw-toothed Waves", AccessRights = OpcAccessRights.Read },
            new OpcTagInfo { TagName = "Saw-toothed Waves.Int2", DisplayName = "Saw Wave Int2", DataType = "Int16", Path = "Saw-toothed Waves", AccessRights = OpcAccessRights.Read },
            new OpcTagInfo { TagName = "Saw-toothed Waves.Int4", DisplayName = "Saw Wave Int4", DataType = "Int32", Path = "Saw-toothed Waves", AccessRights = OpcAccessRights.Read },
            new OpcTagInfo { TagName = "Saw-toothed Waves.Real4", DisplayName = "Saw Wave Real4", DataType = "Single", Path = "Saw-toothed Waves", AccessRights = OpcAccessRights.Read },
            new OpcTagInfo { TagName = "Saw-toothed Waves.Real8", DisplayName = "Saw Wave Real8", DataType = "Double", Path = "Saw-toothed Waves", AccessRights = OpcAccessRights.Read },

            // Square Waves
            new OpcTagInfo { TagName = "Square Waves.Boolean", DisplayName = "Square Wave Boolean", DataType = "Boolean", Path = "Square Waves", AccessRights = OpcAccessRights.Read },

            // Bucket Brigade
            new OpcTagInfo { TagName = "Bucket Brigade.Int1", DisplayName = "Bucket Brigade Int1", DataType = "Byte", Path = "Bucket Brigade", AccessRights = OpcAccessRights.Read },
            new OpcTagInfo { TagName = "Bucket Brigade.Int2", DisplayName = "Bucket Brigade Int2", DataType = "Int16", Path = "Bucket Brigade", AccessRights = OpcAccessRights.Read },
            new OpcTagInfo { TagName = "Bucket Brigade.Int4", DisplayName = "Bucket Brigade Int4", DataType = "Int32", Path = "Bucket Brigade", AccessRights = OpcAccessRights.Read },
            new OpcTagInfo { TagName = "Bucket Brigade.Real4", DisplayName = "Bucket Brigade Real4", DataType = "Single", Path = "Bucket Brigade", AccessRights = OpcAccessRights.Read },
            new OpcTagInfo { TagName = "Bucket Brigade.Real8", DisplayName = "Bucket Brigade Real8", DataType = "Double", Path = "Bucket Brigade", AccessRights = OpcAccessRights.Read },

            // Write Only tags
            new OpcTagInfo { TagName = "WriteOnly.Boolean", DisplayName = "Write Only Boolean", DataType = "Boolean", Path = "WriteOnly", AccessRights = OpcAccessRights.Write },
            new OpcTagInfo { TagName = "WriteOnly.Int4", DisplayName = "Write Only Int4", DataType = "Int32", Path = "WriteOnly", AccessRights = OpcAccessRights.Write },
            new OpcTagInfo { TagName = "WriteOnly.Real8", DisplayName = "Write Only Real8", DataType = "Double", Path = "WriteOnly", AccessRights = OpcAccessRights.Write }
        };

        //_availableTags.AddRange(tags);
    }

    private void LoadGenericTags()
    {
        var tags = new[]
        {
            new OpcTagInfo { TagName = "Process.Temperature", DisplayName = "Temperature", DataType = "Double", Path = "Process", AccessRights = OpcAccessRights.Read },
            new OpcTagInfo { TagName = "Process.Pressure", DisplayName = "Pressure", DataType = "Double", Path = "Process", AccessRights = OpcAccessRights.Read },
            new OpcTagInfo { TagName = "Process.Flow", DisplayName = "Flow Rate", DataType = "Double", Path = "Process", AccessRights = OpcAccessRights.Read },
            new OpcTagInfo { TagName = "Process.Level", DisplayName = "Tank Level", DataType = "Double", Path = "Process", AccessRights = OpcAccessRights.Read },
            new OpcTagInfo { TagName = "Alarms.HighTemp", DisplayName = "High Temperature Alarm", DataType = "Boolean", Path = "Alarms", AccessRights = OpcAccessRights.Read },
            new OpcTagInfo { TagName = "Alarms.LowPressure", DisplayName = "Low Pressure Alarm", DataType = "Boolean", Path = "Alarms", AccessRights = OpcAccessRights.Read },
            new OpcTagInfo { TagName = "Motors.Pump1.Speed", DisplayName = "Pump 1 Speed", DataType = "Int32", Path = "Motors", AccessRights = OpcAccessRights.ReadWrite },
            new OpcTagInfo { TagName = "Motors.Pump1.Running", DisplayName = "Pump 1 Running", DataType = "Boolean", Path = "Motors", AccessRights = OpcAccessRights.ReadWrite }
        };

        //_availableTags.AddRange(tags);
    }
}
