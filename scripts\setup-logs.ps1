# Script para configurar pastas de logs
Write-Host "Configurando estrutura de logs para Smar AssetView Nx..." -ForegroundColor Green

# Criar pasta de logs para o serviço OPC DA
$opcServiceLogsPath = "src\Services\OpcDa\logs"
if (!(Test-Path $opcServiceLogsPath)) {
    New-Item -ItemType Directory -Path $opcServiceLogsPath -Force
    Write-Host "Pasta criada: $opcServiceLogsPath" -ForegroundColor Yellow
} else {
    Write-Host "Pasta já existe: $opcServiceLogsPath" -ForegroundColor Gray
}

# Criar pasta de logs para a aplicação Blazor
$blazorLogsPath = "src\AssetView.Nx.Dashboard\logs"
if (!(Test-Path $blazorLogsPath)) {
    New-Item -ItemType Directory -Path $blazorLogsPath -Force
    Write-Host "Pasta criada: $blazorLogsPath" -ForegroundColor Yellow
} else {
    Write-Host "Pasta já existe: $blazorLogsPath" -ForegroundColor Gray
}

# Criar pasta de logs geral na raiz
$rootLogsPath = "logs"
if (!(Test-Path $rootLogsPath)) {
    New-Item -ItemType Directory -Path $rootLogsPath -Force
    Write-Host "Pasta criada: $rootLogsPath" -ForegroundColor Yellow
} else {
    Write-Host "Pasta já existe: $rootLogsPath" -ForegroundColor Gray
}

# Criar arquivo .gitignore para logs se não existir
$gitignoreContent = @"
# Log files
*.log
*.log.*
logs/
**/logs/
"@

$gitignorePath = ".gitignore"
if (Test-Path $gitignorePath) {
    $existingContent = Get-Content $gitignorePath -Raw
    if ($existingContent -notmatch "logs/") {
        Add-Content -Path $gitignorePath -Value "`n# Log files`n*.log`n*.log.*`nlogs/`n**/logs/"
        Write-Host "Adicionado configuração de logs ao .gitignore" -ForegroundColor Yellow
    } else {
        Write-Host ".gitignore já contém configuração de logs" -ForegroundColor Gray
    }
} else {
    Set-Content -Path $gitignorePath -Value $gitignoreContent
    Write-Host "Criado .gitignore com configuração de logs" -ForegroundColor Yellow
}

Write-Host "`nConfiguração de logs concluída!" -ForegroundColor Green
Write-Host "Logs serão salvos em:" -ForegroundColor Cyan
Write-Host "  - Serviço OPC DA: $opcServiceLogsPath" -ForegroundColor White
Write-Host "  - Aplicação Blazor: $blazorLogsPath" -ForegroundColor White
Write-Host "  - Logs gerais: $rootLogsPath" -ForegroundColor White
