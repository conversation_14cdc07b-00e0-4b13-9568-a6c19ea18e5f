using AssetView.Nx.Modules.OpcDa.Models;

namespace AssetView.Nx.Modules.OpcDa.Interfaces;

/// <summary>
/// Interface para descoberta e gerenciamento de servidores OPC
/// </summary>
public interface IOpcDiscoveryService
{
    /// <summary>
    /// Descobre servidores OPC disponíveis no host especificado
    /// </summary>
    /// <param name="host">Host para buscar servidores (padrão: localhost)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de servidores OPC encontrados</returns>
    Task<IEnumerable<OpcServerInfo>> DiscoverServersAsync(string host = "localhost", CancellationToken cancellationToken = default);

    /// <summary>
    /// Descobre servidores OPC em múltiplos hosts da rede
    /// </summary>
    /// <param name="hosts">Lista de hosts para buscar</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de servidores OPC encontrados em todos os hosts</returns>
    Task<IEnumerable<OpcServerInfo>> DiscoverServersInNetworkAsync(IEnumerable<string> hosts, CancellationToken cancellationToken = default);

    /// <summary>
    /// Força uma nova descoberta e atualiza o cache de servidores
    /// </summary>
    /// <param name="host">Host para buscar servidores</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista atualizada de servidores OPC</returns>
    Task<IEnumerable<OpcServerInfo>> RefreshServerDiscoveryAsync(string host = "localhost", CancellationToken cancellationToken = default);

    /// <summary>
    /// Verifica se um servidor OPC específico está disponível
    /// </summary>
    /// <param name="progId">ProgID do servidor</param>
    /// <param name="host">Host do servidor</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se o servidor estiver disponível</returns>
    Task<bool> IsServerAvailableAsync(string progId, string host = "localhost", CancellationToken cancellationToken = default);

    /// <summary>
    /// Conecta a um servidor OPC específico
    /// </summary>
    /// <param name="serverInfo">Informações do servidor</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se conectado com sucesso</returns>
    Task<bool> ConnectToServerAsync(OpcServerInfo serverInfo, CancellationToken cancellationToken = default);

    /// <summary>
    /// Desconecta do servidor OPC atual
    /// </summary>
    /// <param name="cancellationToken">Token de cancelamento</param>
    Task DisconnectFromServerAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Navega pela estrutura de tags do servidor conectado
    /// </summary>
    /// <param name="path">Caminho para navegar (vazio para raiz)</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de tags e branches no caminho especificado</returns>
    Task<IEnumerable<OpcTagInfo>> BrowseTagsAsync(string path = "", CancellationToken cancellationToken = default);

    /// <summary>
    /// Busca tags por nome ou padrão
    /// </summary>
    /// <param name="searchPattern">Padrão de busca</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de tags que correspondem ao padrão</returns>
    Task<IEnumerable<OpcTagInfo>> SearchTagsAsync(string searchPattern, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém todos os tags disponíveis/selecionados
    /// </summary>
    /// <returns>Lista de todos os tags disponíveis</returns>
    Task<IEnumerable<OpcTagInfo>> GetAvailableTagsAsync();

    /// <summary>
    /// Adiciona um tag à lista de tags disponíveis/selecionados
    /// </summary>
    /// <param name="tagInfo">Informações do tag a ser adicionado</param>
    /// <returns>True se o tag foi adicionado com sucesso</returns>
    Task<bool> AddAvailableTagAsync(OpcTagInfo tagInfo);

    /// <summary>
    /// Adiciona um tag ao monitoramento
    /// </summary>
    /// <param name="request">Solicitação para adicionar tag</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se adicionado com sucesso</returns>
    Task<bool> AddTagToMonitoringAsync(AddTagRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove um tag do monitoramento
    /// </summary>
    /// <param name="tagName">Nome do tag</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se removido com sucesso</returns>
    Task<bool> RemoveTagFromMonitoringAsync(string tagName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém informações do servidor atualmente conectado
    /// </summary>
    /// <returns>Informações do servidor ou null se não conectado</returns>
    OpcServerInfo? GetCurrentServerInfo();

    /// <summary>
    /// Obtém lista de grupos disponíveis para adicionar tags
    /// </summary>
    /// <returns>Lista de nomes de grupos</returns>
    Task<IEnumerable<string>> GetAvailableGroupsAsync();

    /// <summary>
    /// Cria um novo grupo para monitoramento
    /// </summary>
    /// <param name="groupName">Nome do grupo</param>
    /// <param name="updateRate">Taxa de atualização em ms</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>True se criado com sucesso</returns>
    Task<bool> CreateGroupAsync(string groupName, int updateRate = 1000, CancellationToken cancellationToken = default);
}
