using AssetView.Nx.Core.Entities;
using AssetView.Nx.Core.Interfaces;
using AssetView.Nx.Shared.Models;
using Microsoft.Extensions.Logging;

namespace AssetView.Nx.Modules.Users.Services;

/// <summary>
/// Implementação do serviço de gestão de roles
/// </summary>
public class RoleManagementService : IRoleManagementService
{
    private readonly ITenantRepository<ApplicationRole> _roleRepository;
    private readonly ITenantRepository<ApplicationUser> _userRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ITenantService _tenantService;
    private readonly ILogger<RoleManagementService> _logger;

    public RoleManagementService(
        ITenantRepository<ApplicationRole> roleRepository,
        ITenantRepository<ApplicationUser> userRepository,
        IUnitOfWork unitOfWork,
        ITenantService tenantService,
        ILogger<RoleManagementService> logger)
    {
        _roleRepository = roleRepository;
        _userRepository = userRepository;
        _unitOfWork = unitOfWork;
        _tenantService = tenantService;
        _logger = logger;
    }

    public async Task<IEnumerable<ApplicationRole>> GetRolesAsync()
    {
        return await _roleRepository.GetAllAsync();
    }

    public async Task<PagedResult<ApplicationRole>> GetRolesPagedAsync(int page, int pageSize, string? searchTerm = null, bool? isActive = null)
    {
        try
        {
            var (roles, totalCount) = await _roleRepository.GetPagedAsync(
                page,
                pageSize,
                predicate: BuildPredicate(searchTerm, isActive),
                orderBy: r => r.Name
            );

            return new PagedResult<ApplicationRole>
            {
                Items = roles,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter roles paginadas");
            return new PagedResult<ApplicationRole>();
        }
    }

    public async Task<ApplicationRole?> GetRoleByIdAsync(string roleId)
    {
        return await _roleRepository.GetByIdAsync(roleId);
    }

    public async Task<RoleResult> CreateRoleAsync(CreateRoleRequest request)
    {
        try
        {
            // Validar se role já existe
            var existingRole = await _roleRepository.GetFirstOrDefaultAsync(r => r.Name == request.Name);
            if (existingRole != null)
            {
                return new RoleResult
                {
                    Success = false,
                    Message = "Já existe uma role com este nome"
                };
            }

            var role = new ApplicationRole
            {
                Name = request.Name,
                NormalizedName = request.Name.ToUpperInvariant(),
                Description = request.Description,
                IsActive = request.IsActive,
                IsSystemRole = false
            };

            await _roleRepository.AddAsync(role);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Role {RoleName} criada com sucesso", request.Name);

            return new RoleResult
            {
                Success = true,
                Message = "Role criada com sucesso",
                Role = role
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao criar role {RoleName}", request.Name);
            return new RoleResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    public async Task<RoleResult> UpdateRoleAsync(string roleId, UpdateRoleRequest request)
    {
        try
        {
            var role = await _roleRepository.GetByIdAsync(roleId);
            if (role == null)
            {
                return new RoleResult
                {
                    Success = false,
                    Message = "Role não encontrada"
                };
            }

            if (role.IsSystemRole)
            {
                return new RoleResult
                {
                    Success = false,
                    Message = "Roles do sistema não podem ser editadas"
                };
            }

            // Validar se nome já existe (exceto para a role atual)
            var existingRole = await _roleRepository.GetFirstOrDefaultAsync(r => r.Name == request.Name && r.Id != roleId);
            if (existingRole != null)
            {
                return new RoleResult
                {
                    Success = false,
                    Message = "Já existe uma role com este nome"
                };
            }

            role.Name = request.Name;
            role.NormalizedName = request.Name.ToUpperInvariant();
            role.Description = request.Description;
            role.IsActive = request.IsActive;

            await _roleRepository.UpdateAsync(role);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Role {RoleId} atualizada com sucesso", roleId);

            return new RoleResult
            {
                Success = true,
                Message = "Role atualizada com sucesso",
                Role = role
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao atualizar role {RoleId}", roleId);
            return new RoleResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    public async Task<RoleResult> ToggleRoleStatusAsync(string roleId)
    {
        try
        {
            var role = await _roleRepository.GetByIdAsync(roleId);
            if (role == null)
            {
                return new RoleResult
                {
                    Success = false,
                    Message = "Role não encontrada"
                };
            }

            if (role.IsSystemRole)
            {
                return new RoleResult
                {
                    Success = false,
                    Message = "Roles do sistema não podem ser desativadas"
                };
            }

            role.IsActive = !role.IsActive;
            await _roleRepository.UpdateAsync(role);
            await _unitOfWork.SaveChangesAsync();

            var status = role.IsActive ? "ativada" : "desativada";
            _logger.LogInformation("Role {RoleId} {Status}", roleId, status);

            return new RoleResult
            {
                Success = true,
                Message = $"Role {status} com sucesso",
                Role = role
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao alterar status da role {RoleId}", roleId);
            return new RoleResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    public async Task<RoleResult> DeleteRoleAsync(string roleId)
    {
        try
        {
            var role = await _roleRepository.GetByIdAsync(roleId);
            if (role == null)
            {
                return new RoleResult
                {
                    Success = false,
                    Message = "Role não encontrada"
                };
            }

            if (role.IsSystemRole)
            {
                return new RoleResult
                {
                    Success = false,
                    Message = "Roles do sistema não podem ser excluídas"
                };
            }

            if (!await CanDeleteRoleAsync(roleId))
            {
                return new RoleResult
                {
                    Success = false,
                    Message = "Esta role não pode ser excluída pois está sendo usada por usuários"
                };
            }

            role.IsDeleted = true;
            role.DeletedAt = DateTime.UtcNow;
            await _roleRepository.UpdateAsync(role);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Role {RoleId} excluída com sucesso", roleId);

            return new RoleResult
            {
                Success = true,
                Message = "Role excluída com sucesso"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao excluir role {RoleId}", roleId);
            return new RoleResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    public async Task<bool> CanDeleteRoleAsync(string roleId)
    {
        // Por simplicidade, sempre permitimos exclusão
        // Em uma implementação completa, verificaríamos se há usuários com esta role
        return await Task.FromResult(true);
    }

    public async Task<IEnumerable<ApplicationUser>> GetUsersInRoleAsync(string roleId)
    {
        // Por simplicidade, retornamos uma lista vazia
        // Em uma implementação completa, teríamos uma tabela de relacionamento UserRoles
        return await Task.FromResult(new List<ApplicationUser>());
    }

    private System.Linq.Expressions.Expression<Func<ApplicationRole, bool>>? BuildPredicate(string? searchTerm, bool? isActive)
    {
        if (string.IsNullOrEmpty(searchTerm) && !isActive.HasValue)
            return null;

        return role => 
            (string.IsNullOrEmpty(searchTerm) || role.Name.Contains(searchTerm) || (role.Description != null && role.Description.Contains(searchTerm))) &&
            (!isActive.HasValue || role.IsActive == isActive.Value);
    }
}
