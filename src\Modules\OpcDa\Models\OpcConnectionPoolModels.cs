using Opc.Da;
using AssetView.Nx.Modules.OpcDa.Services;

namespace AssetView.Nx.Modules.OpcDa.Models;

/// <summary>
/// Informações de uma conexão no pool
/// </summary>
internal class OpcPooledConnectionInfo
{
    /// <summary>
    /// ID único da conexão
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Servidor OPC
    /// </summary>
    public Server Server { get; set; } = null!;

    /// <summary>
    /// Timestamp de criação
    /// </summary>
    public DateTime Created { get; set; }

    /// <summary>
    /// Timestamp do último uso
    /// </summary>
    public DateTime LastUsed { get; set; }

    /// <summary>
    /// Host do servidor
    /// </summary>
    public string Host { get; set; } = string.Empty;

    /// <summary>
    /// ProgID do servidor
    /// </summary>
    public string ServerProgId { get; set; } = string.Empty;

    /// <summary>
    /// Indica se a conexão está em uso
    /// </summary>
    public bool InUse { get; set; }

    /// <summary>
    /// Número de vezes que foi usada
    /// </summary>
    public int UseCount { get; set; }

    /// <summary>
    /// Tempo total de uso
    /// </summary>
    public TimeSpan TotalUsageTime { get; set; }
}

/// <summary>
/// Interface para conexão do pool
/// </summary>
public interface IOpcPooledConnection : IDisposable
{
    /// <summary>
    /// Servidor OPC
    /// </summary>
    Server Server { get; }

    /// <summary>
    /// ID da conexão
    /// </summary>
    string ConnectionId { get; }

    /// <summary>
    /// Host do servidor
    /// </summary>
    string Host { get; }

    /// <summary>
    /// ProgID do servidor
    /// </summary>
    string ServerProgId { get; }

    /// <summary>
    /// Indica se a conexão está válida
    /// </summary>
    bool IsValid { get; }

    /// <summary>
    /// Timestamp de criação
    /// </summary>
    DateTime Created { get; }

    /// <summary>
    /// Timestamp do último uso
    /// </summary>
    DateTime LastUsed { get; }
}

/// <summary>
/// Wrapper para conexão do pool
/// </summary>
internal class OpcPooledConnectionWrapper : IOpcPooledConnection
{
    private readonly OpcPooledConnectionInfo _connectionInfo;
    private readonly OpcConnectionPool _pool;
    private readonly OpcPerformanceMetricsService? _metricsService;
    private readonly DateTime _usageStartTime;
    private bool _disposed = false;

    public Server Server => _connectionInfo.Server;
    public string ConnectionId => _connectionInfo.Id;
    public string Host => _connectionInfo.Host;
    public string ServerProgId => _connectionInfo.ServerProgId;
    public DateTime Created => _connectionInfo.Created;
    public DateTime LastUsed => _connectionInfo.LastUsed;

    public bool IsValid
    {
        get
        {
            try
            {
                var status = _connectionInfo.Server.GetStatus();
                return status != null;
            }
            catch
            {
                return false;
            }
        }
    }

    public OpcPooledConnectionWrapper(
        OpcPooledConnectionInfo connectionInfo,
        OpcConnectionPool pool,
        OpcPerformanceMetricsService? metricsService)
    {
        _connectionInfo = connectionInfo;
        _pool = pool;
        _metricsService = metricsService;
        _usageStartTime = DateTime.UtcNow;

        _connectionInfo.InUse = true;
        _connectionInfo.UseCount++;
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            try
            {
                // Calcular tempo de uso
                var usageTime = DateTime.UtcNow - _usageStartTime;
                _connectionInfo.TotalUsageTime += usageTime;
                _connectionInfo.InUse = false;
                _connectionInfo.LastUsed = DateTime.UtcNow;

                // Registrar métricas de uso
                _metricsService?.RecordOperation("UsePooledConnection", usageTime, true, _connectionInfo.ServerProgId);

                // Retornar conexão ao pool
                _pool.ReturnConnection(_connectionInfo.Id);
            }
            catch (Exception ex)
            {
                // Log error but don't throw in Dispose
                // _logger.LogWarning(ex, "Erro ao retornar conexão ao pool");
            }
            finally
            {
                _disposed = true;
            }
        }
    }
}

/// <summary>
/// Configuração avançada do pool de conexões
/// </summary>
public class OpcAdvancedPoolConfig : OpcConnectionPoolConfig
{
    /// <summary>
    /// Habilitar pré-aquecimento do pool
    /// </summary>
    public bool EnableWarmup { get; set; } = false;

    /// <summary>
    /// Número de conexões para pré-aquecer
    /// </summary>
    public int WarmupConnections { get; set; } = 2;

    /// <summary>
    /// Habilitar validação de conexões
    /// </summary>
    public bool EnableConnectionValidation { get; set; } = true;

    /// <summary>
    /// Intervalo de validação de conexões
    /// </summary>
    public TimeSpan ValidationInterval { get; set; } = TimeSpan.FromMinutes(2);

    /// <summary>
    /// Habilitar balanceamento de carga entre conexões
    /// </summary>
    public bool EnableLoadBalancing { get; set; } = false;

    /// <summary>
    /// Estratégia de balanceamento
    /// </summary>
    public PoolLoadBalancingStrategy LoadBalancingStrategy { get; set; } = PoolLoadBalancingStrategy.RoundRobin;

    /// <summary>
    /// Habilitar métricas detalhadas do pool
    /// </summary>
    public bool EnableDetailedMetrics { get; set; } = true;

    /// <summary>
    /// Limite de uso por conexão antes de recriá-la
    /// </summary>
    public int MaxUsagePerConnection { get; set; } = 1000;

    /// <summary>
    /// Habilitar retry automático em falhas de conexão
    /// </summary>
    public bool EnableAutoRetry { get; set; } = true;

    /// <summary>
    /// Número máximo de tentativas de retry
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Delay entre tentativas de retry
    /// </summary>
    public TimeSpan RetryDelay { get; set; } = TimeSpan.FromSeconds(1);
}

/// <summary>
/// Estratégias de balanceamento de carga do pool
/// </summary>
public enum PoolLoadBalancingStrategy
{
    /// <summary>
    /// Round Robin - rotação circular
    /// </summary>
    RoundRobin,

    /// <summary>
    /// Menos usado - conexão com menor uso
    /// </summary>
    LeastUsed,

    /// <summary>
    /// Mais recente - conexão criada mais recentemente
    /// </summary>
    MostRecent,

    /// <summary>
    /// Aleatório
    /// </summary>
    Random
}

/// <summary>
/// Evento do pool de conexões
/// </summary>
public class OpcPoolEvent
{
    /// <summary>
    /// Timestamp do evento
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Tipo do evento
    /// </summary>
    public OpcPoolEventType EventType { get; set; }

    /// <summary>
    /// Chave do pool
    /// </summary>
    public string PoolKey { get; set; } = string.Empty;

    /// <summary>
    /// ID da conexão (se aplicável)
    /// </summary>
    public string? ConnectionId { get; set; }

    /// <summary>
    /// Mensagem do evento
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Dados adicionais
    /// </summary>
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}

/// <summary>
/// Tipos de evento do pool
/// </summary>
public enum OpcPoolEventType
{
    /// <summary>
    /// Conexão criada
    /// </summary>
    ConnectionCreated,

    /// <summary>
    /// Conexão destruída
    /// </summary>
    ConnectionDestroyed,

    /// <summary>
    /// Conexão obtida do pool
    /// </summary>
    ConnectionAcquired,

    /// <summary>
    /// Conexão retornada ao pool
    /// </summary>
    ConnectionReturned,

    /// <summary>
    /// Pool criado
    /// </summary>
    PoolCreated,

    /// <summary>
    /// Pool destruído
    /// </summary>
    PoolDestroyed,

    /// <summary>
    /// Manutenção do pool
    /// </summary>
    PoolMaintenance,

    /// <summary>
    /// Erro no pool
    /// </summary>
    PoolError,

    /// <summary>
    /// Pool esgotado
    /// </summary>
    PoolExhausted,

    /// <summary>
    /// Conexão inválida detectada
    /// </summary>
    InvalidConnectionDetected
}

/// <summary>
/// Métricas detalhadas de uma conexão do pool
/// </summary>
public class OpcPoolConnectionMetrics
{
    /// <summary>
    /// ID da conexão
    /// </summary>
    public string ConnectionId { get; set; } = string.Empty;

    /// <summary>
    /// Número total de usos
    /// </summary>
    public int TotalUses { get; set; }

    /// <summary>
    /// Tempo total de uso
    /// </summary>
    public TimeSpan TotalUsageTime { get; set; }

    /// <summary>
    /// Tempo médio de uso
    /// </summary>
    public TimeSpan AverageUsageTime => TotalUses > 0 ? 
        TimeSpan.FromMilliseconds(TotalUsageTime.TotalMilliseconds / TotalUses) : TimeSpan.Zero;

    /// <summary>
    /// Timestamp de criação
    /// </summary>
    public DateTime Created { get; set; }

    /// <summary>
    /// Último uso
    /// </summary>
    public DateTime LastUsed { get; set; }

    /// <summary>
    /// Tempo de vida da conexão
    /// </summary>
    public TimeSpan Lifetime => DateTime.UtcNow - Created;

    /// <summary>
    /// Tempo desde o último uso
    /// </summary>
    public TimeSpan TimeSinceLastUse => DateTime.UtcNow - LastUsed;

    /// <summary>
    /// Indica se está atualmente em uso
    /// </summary>
    public bool InUse { get; set; }

    /// <summary>
    /// Host do servidor
    /// </summary>
    public string Host { get; set; } = string.Empty;

    /// <summary>
    /// ProgID do servidor
    /// </summary>
    public string ServerProgId { get; set; } = string.Empty;
}
