using System.Runtime.InteropServices;
using Microsoft.Extensions.Logging;
using AssetView.Nx.Modules.OpcDa.Interfaces;
using AssetView.Nx.Modules.OpcDa.Models;
using AssetView.Nx.Modules.OpcDa.DTOs;
using AssetView.Nx.Modules.OpcDa.Com;
using Timer = System.Threading.Timer;
using Opc;
using Opc.Da;
using OpcCom;

namespace AssetView.Nx.Modules.OpcDa.Services;

/// <summary>
/// Implementação do serviço OPC DA usando OpcNetApi.Com com métricas de performance
/// </summary>
public class OpcDaService : IOpcDaService, IDisposable
{
    private readonly ILogger<OpcDaService> _logger;
    private readonly OpcPerformanceMetricsService? _metricsService;
    private Opc.Da.Server? _opcServer;
    private readonly Dictionary<string, Opc.Da.ISubscription> _groups = new();
    private readonly Dictionary<string, OpcDataItem> _items = new();
    private OpcConnectionStatus _connectionStatus = OpcConnectionStatus.Disconnected;
    private OpcServerConfig? _config;
    private readonly Timer _reconnectTimer;
    private Timer? _simulationTimer;
    private int _reconnectAttempts = 0;
    private readonly object _lockObject = new();
    private long _totalUpdates = 0;
    private DateTime _startTime = DateTime.UtcNow;
    private DateTime _connectionStartTime;
    private int _connectionErrors = 0;
    private DateTime? _lastError;
    private readonly Random _random = new();
    private readonly OpcCom.Factory _factory;

    public event EventHandler<OpcDataUpdateEventArgs>? DataUpdated;
    public event EventHandler<AssetView.Nx.Modules.OpcDa.Interfaces.OpcConnectionStatusEventArgs>? ConnectionStatusChanged;

    public OpcDaService(ILogger<OpcDaService> logger)
    {
        _logger = logger;
        _metricsService = null; // Será injetado via propriedade se necessário
        _factory = new OpcCom.Factory();
        _reconnectTimer = new Timer(ReconnectCallback, null, Timeout.Infinite, Timeout.Infinite);
    }

    public async Task<bool> ConnectAsync(OpcServerConfig config, CancellationToken cancellationToken = default)
    {
        var metrics = _metricsService != null ? (OpcOperationMeasurement)_metricsService.StartOperation("Connect", config.ProgId, $"{config.Host}:{config.ProgId}") : null;
        _connectionStartTime = DateTime.UtcNow;

        try
        {
            _config = config;
            _logger.LogInformation("Conectando ao servidor OPC: {ProgId} no host: {Host}", config.ProgId, config.Host);

            SetConnectionStatus(OpcConnectionStatus.Connecting);

            // Criar URL do servidor OPC
            var url = new Opc.URL($"opcda://{config.Host}/{config.ProgId}");
            _logger.LogDebug("URL do servidor OPC: {Url}", url.ToString());

            // Criar instância do servidor usando OpcNetApi.Com
            _opcServer = new Opc.Da.Server(_factory, url);

            // Configurar dados de conexão
            var connectData = new Opc.ConnectData(new System.Net.NetworkCredential());

            // Configurar timeout se especificado
            // Nota: ConnectTimeout não está disponível na versão atual do OpcNetApi
            // if (config.ConnectionTimeout > 0)
            // {
            //     connectData.ConnectTimeout = config.ConnectionTimeout;
            // }

            // Conectar ao servidor
            await Task.Run(() =>
            {
                try
                {
                    _opcServer.Connect(connectData);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erro ao conectar ao servidor OPC");
                }
            }, cancellationToken);

            _logger.LogInformation("Conectado com sucesso ao servidor OPC: {ProgId}", config.ProgId);

            // Verificar se há grupos para adicionar
            if (config.Groups == null || !config.Groups.Any())
            {
                _logger.LogWarning("Nenhum grupo configurado para o servidor OPC");
                SetConnectionStatus(OpcConnectionStatus.Connected);
                return true;
            }

            // Adicionar grupos configurados
            _logger.LogInformation("Adicionando {GroupCount} grupos ao servidor OPC", config.Groups.Count);
            foreach (var group in config.Groups)
            {
                try
                {
                    _logger.LogDebug("Adicionando grupo: {GroupName}", group.Name);
                    var success = await AddGroupAsync(group, cancellationToken);
                    if (!success)
                    {
                        _logger.LogWarning("Falha ao adicionar grupo: {GroupName}", group.Name);
                    }
                }
                catch (Exception groupEx)
                {
                    _logger.LogError(groupEx, "Erro ao adicionar grupo: {GroupName}", group.Name);
                    // Continuar com outros grupos mesmo se um falhar
                }
            }

            SetConnectionStatus(OpcConnectionStatus.Connected);
            _reconnectAttempts = 0;
            _startTime = DateTime.UtcNow;

            // Registrar métricas de conexão bem-sucedida
            var connectionTime = DateTime.UtcNow - _connectionStartTime;
            _metricsService?.RecordConnection(config.ProgId, config.Host, true, connectionTime);
            metrics?.Complete(true);
            metrics?.Dispose();

            _logger.LogInformation("Conectado com sucesso ao servidor OPC: {ProgId} em {Duration}ms",
                config.ProgId, connectionTime.TotalMilliseconds);
            return true;
        }
        catch (Opc.ConnectFailedException connectEx)
        {
            var connectionTime = DateTime.UtcNow - _connectionStartTime;
            var errorMessage = $"Falha na conexão: {connectEx.Message}";

            _logger.LogError("Falha na conexão OPC: {Message} (Código: {ResultId})", connectEx.Message, connectEx.Result);
            SetConnectionStatus(OpcConnectionStatus.Error, errorMessage);
            _connectionErrors++;
            _lastError = DateTime.UtcNow;

            // Registrar métricas de falha de conexão
            _metricsService?.RecordConnection(config.ProgId, config.Host, false, connectionTime, errorMessage);
            metrics?.Complete(false, errorMessage);
            metrics?.Dispose();

            return false;
        }
        catch (COMException comEx)
        {
            var connectionTime = DateTime.UtcNow - _connectionStartTime;
            var errorMessage = $"Erro COM: {comEx.Message}";

            _logger.LogError("Erro COM ao conectar: {Message} (HRESULT: 0x{HResult:X8})", comEx.Message, comEx.HResult);
            SetConnectionStatus(OpcConnectionStatus.Error, errorMessage);
            _connectionErrors++;
            _lastError = DateTime.UtcNow;

            // Registrar métricas de falha de conexão
            _metricsService?.RecordConnection(config.ProgId, config.Host, false, connectionTime, errorMessage);
            metrics?.Complete(false, errorMessage);
            metrics?.Dispose();

            return false;
        }
        catch (Exception ex)
        {
            var connectionTime = DateTime.UtcNow - _connectionStartTime;
            var errorMessage = ex.Message;

            _logger.LogError(ex, "Erro ao conectar ao servidor OPC: {ProgId}", config.ProgId);
            SetConnectionStatus(OpcConnectionStatus.Error, errorMessage);
            _connectionErrors++;
            _lastError = DateTime.UtcNow;

            // Registrar métricas de falha de conexão
            _metricsService?.RecordConnection(config.ProgId, config.Host, false, connectionTime, errorMessage);
            metrics?.Complete(false, errorMessage);
            metrics?.Dispose();

            // Iniciar timer de reconexão
            if (_config != null && _reconnectAttempts < _config.MaxReconnectAttempts)
            {
                _reconnectTimer.Change(_config.ReconnectInterval, Timeout.Infinite);
            }

            return false;
        }
    }

    public async Task DisconnectAsync(CancellationToken cancellationToken = default)
    {
        var metrics = _metricsService != null ? (OpcOperationMeasurement)_metricsService.StartOperation("Disconnect", _config?.ProgId) : null;
        var disconnectStartTime = DateTime.UtcNow;

        try
        {
            _logger.LogInformation("Desconectando do servidor OPC");

            lock (_lockObject)
            {
                // Parar timer de reconexão
                _reconnectTimer.Change(Timeout.Infinite, Timeout.Infinite);

                // Limpar grupos usando OpcNetApi
                foreach (var group in _groups.Values)
                {
                    try
                    {
                        group.Dispose();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Erro ao liberar grupo OPC");
                    }
                }
                _groups.Clear();

                // Desconectar servidor usando OpcNetApi
                if (_opcServer != null)
                {
                    try
                    {
                        _opcServer.Disconnect();
                        _opcServer.Dispose();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Erro ao desconectar servidor OPC");
                    }
                    finally
                    {
                        _opcServer = null;
                    }
                }

                _items.Clear();
                SetConnectionStatus(OpcConnectionStatus.Disconnected);
            }

            // Registrar métricas de desconexão
            if (_config != null)
            {
                var sessionDuration = DateTime.UtcNow - _startTime;
                _metricsService?.RecordDisconnection(_config.ProgId, _config.Host, sessionDuration);
            }

            metrics?.Complete(true);
            metrics?.Dispose();
            _logger.LogInformation("Desconectado do servidor OPC");
        }
        catch (Exception ex)
        {
            metrics?.Complete(false, ex.Message);
            metrics?.Dispose();
            _logger.LogError(ex, "Erro ao desconectar do servidor OPC");
        }

        await Task.CompletedTask;
    }

    public async Task<bool> AddGroupAsync(OpcGroup group, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_opcServer == null)
            {
                _logger.LogWarning("Servidor OPC não conectado");
                return false;
            }

            _logger.LogInformation("Adicionando grupo: {GroupName}, Taxa: {UpdateRate}ms", group.Name, group.UpdateRate);

            // Verificar se o grupo já existe
            if (_groups.ContainsKey(group.Name))
            {
                _logger.LogWarning("Grupo já existe: {GroupName}", group.Name);
                return false;
            }

            // Configurar estado da assinatura
            var groupState = new Opc.Da.SubscriptionState
            {
                Name = group.Name,
                Active = group.IsActive,
                UpdateRate = group.UpdateRate,
                Deadband = 0.0f,
                KeepAlive = 0
            };

            // Criar assinatura no servidor OPC
            var subscription = (Opc.Da.Subscription)_opcServer.CreateSubscription(groupState);
            
            // Registrar callback para mudanças de dados (apenas uma vez por grupo)
            subscription.DataChanged += OnDataChanged;
            
            // Armazenar assinatura
            _groups[group.Name] = subscription;

            // Adicionar itens se houver
            if (group.Items.Any())
            {
                await AddItemsToGroupAsync(group.Name, group.Items, cancellationToken);
            }

            _logger.LogInformation("Grupo adicionado com sucesso: {GroupName}, Taxa: {UpdateRate}ms", group.Name, groupState.UpdateRate);

            return true;
        }
        catch (Exception opcEx) when (opcEx.GetType().Name.Contains("Result"))
        {
            _logger.LogError("Erro OPC ao adicionar grupo {GroupName}: {Message}", group.Name, opcEx.Message);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao adicionar grupo: {GroupName}", group.Name);
            return false;
        }
    }

    // Método centralizado para tratamento de eventos de dados
    private void OnDataChanged(object? sender, object requestHandle, ItemValueResult[] itemValues)
    {
        try
        {
            _logger.LogDebug("OnDataChanged recebido com {Count} valores", itemValues.Length);
            
            if (itemValues.Length == 0)
            {
                _logger.LogWarning("Evento OnDataChanged recebido sem valores");
                return;
            }
            
            lock (_lockObject)
            {
                foreach (var itemValue in itemValues)
                {
                    var valueKey = itemValue.ClientHandle.ToString();

                    if (!string.IsNullOrEmpty(valueKey) && _items.TryGetValue(valueKey, out var item))
                    {
                        var oldValue = item.Value;
                        item.Value = itemValue.Value;
                        item.Quality = ConvertOpcQuality(itemValue.Quality);
                        item.Timestamp = itemValue.Timestamp;

                        // Incrementar contador de atualizações
                        _totalUpdates++;

                        // Disparar evento para UI
                        DataUpdated?.Invoke(this, new OpcDataUpdateEventArgs(item));
                        _logger.LogDebug("Dados atualizados para item {TagName}: Valor={Value}, Antigo={OldValue}, Qualidade={Quality}", 
                            item.TagName, item.Value, oldValue, item.Quality);
                    }
                    else
                    {
                        _logger.LogWarning("Recebido evento para item não encontrado. ClientHandle={ClientHandle}", valueKey);
                        
                        // Listar todos os ClientHandles disponíveis para depuração
                        var availableHandles = string.Join(", ", _items.Keys);
                        _logger.LogDebug("ClientHandles disponíveis: {AvailableHandles}", availableHandles);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao processar atualização de dados OPC");
        }
    }

    // Método para converter qualidade OPC para nosso enum
    private OpcQuality ConvertOpcQuality(Opc.Da.Quality quality)
    {
        if (quality.QualityBits == Opc.Da.qualityBits.good)
            return OpcQuality.Good;
        else if (quality.QualityBits == Opc.Da.qualityBits.uncertain)
            return OpcQuality.Uncertain;
        else
            return OpcQuality.Bad;
    }

    public Task<bool> RemoveGroupAsync(string groupName, CancellationToken cancellationToken = default)
    {
        try
        {
            if (_opcServer == null || !_groups.ContainsKey(groupName))
            {
                return Task.FromResult(false);
            }

            var group = _groups[groupName];

            // Remover itens do dicionário
            var itemsToRemove = _items.Where(kvp => kvp.Value.GroupName == groupName).ToList();
            foreach (var item in itemsToRemove)
            {
                _items.Remove(item.Key);
            }

            // Remover grupo usando OpcNetApi
            try
            {
                group.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Erro ao liberar grupo: {GroupName}", groupName);
            }

            _groups.Remove(groupName);

            _logger.LogInformation("Grupo removido: {GroupName}", groupName);
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao remover grupo: {GroupName}", groupName);
            return Task.FromResult(false);
        }
    }

    public bool GroupExists(string groupName)
    {
        return _groups.ContainsKey(groupName);
    }

    public async Task<bool> AddItemToGroupAsync(string groupName, OpcDataItem item, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Adicionando item {TagName} ao grupo existente {GroupName}", item.TagName, groupName);

            if (!_groups.TryGetValue(groupName, out var group))
            {
                _logger.LogWarning("Grupo {GroupName} não encontrado para adicionar item", groupName);
                return false;
            }

            // Adicionar item ao grupo usando o método privado existente
            await AddItemsToGroupAsync(groupName, new List<OpcDataItem> { item }, cancellationToken);

            _logger.LogInformation("Item {TagName} adicionado com sucesso ao grupo {GroupName}", item.TagName, groupName);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao adicionar item {TagName} ao grupo {GroupName}", item.TagName, groupName);
            return false;
        }
    }

    private Task AddItemsToGroupAsync(string groupName, List<OpcDataItem> items, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Iniciando adição de {ItemCount} itens ao grupo {GroupName}", items.Count, groupName);

            if (!_groups.TryGetValue(groupName, out var subscription))
            {
                _logger.LogWarning("Grupo {GroupName} não encontrado para adicionar itens", groupName);
                return Task.CompletedTask;
            }

            if (items == null || items.Count == 0)
            {
                _logger.LogWarning("Nenhum item para adicionar ao grupo {GroupName}", groupName);
                return Task.CompletedTask;
            }

            // Create OPC items for subscription
            var itemDefinitions = items.Select(item => new Opc.Da.Item
            {
                ItemName = item.TagName,
                ClientHandle = item.TagName, // Usar TagName como handle para facilitar identificação
                Active = true
            }).ToArray();

            // Add items to subscription
            var results = ((Opc.Da.Subscription)subscription).AddItems(itemDefinitions);

            // Verificar resultados e atualizar itens locais
            for (int i = 0; i < results.Length; i++)
            {
                var result = results[i];
                var item = items[i];
                
                if (result.ResultID.Succeeded())
                {
                    item.GroupName = groupName;
                    item.Timestamp = DateTime.UtcNow;
                    item.Quality = OpcQuality.Good;
                    _items[item.TagName] = item;
                    _logger.LogDebug("Item {TagName} adicionado com sucesso", item.TagName);
                }
                else
                {
                    _logger.LogWarning("Falha ao adicionar item {TagName}: {Error}", item.TagName, result.ResultID);
                }
            }

            _logger.LogInformation("{SuccessCount} de {TotalCount} itens adicionados ao grupo {GroupName}",
                results.Count(r => r.ResultID.Succeeded()), items.Count, groupName);
        }
        catch (Exception opcEx) when (opcEx.GetType().Name.Contains("Result"))
        {
            _logger.LogError("Erro OPC ao adicionar itens ao grupo {GroupName}: {Message}",
                groupName, opcEx.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao adicionar itens ao grupo: {GroupName}", groupName);
        }

        return Task.CompletedTask;
    }

    public Task<OpcDataItem?> ReadItemAsync(string tagName, CancellationToken cancellationToken = default)
    {
        try
        {
            if (!_items.TryGetValue(tagName, out var item) || item.GroupName == null)
            {
                return Task.FromResult<OpcDataItem?>(null);
            }

            if (!_groups.TryGetValue(item.GroupName, out var group))
            {
                return Task.FromResult<OpcDataItem?>(null);
            }

            // Implementar leitura síncrona usando IOPCSyncIO
            var syncIO = group as IOPCSyncIO;
            if (syncIO == null)
            {
                return Task.FromResult<OpcDataItem?>(null);
            }

            // Esta é uma implementação simplificada
            // Em uma implementação completa, você precisaria gerenciar handles de servidor

            return Task.FromResult<OpcDataItem?>(item);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao ler item: {TagName}", tagName);
            return Task.FromResult<OpcDataItem?>(null);
        }
    }

    public Task<bool> WriteItemAsync(string tagName, object value, CancellationToken cancellationToken = default)
    {
        try
        {
            // Implementação de escrita seria similar à leitura
            // usando IOPCSyncIO.Write
            _logger.LogInformation("Escrevendo valor {Value} no item {TagName}", value, tagName);
            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao escrever item: {TagName}", tagName);
            return Task.FromResult(false);
        }
    }

    public OpcConnectionStatus GetConnectionStatus()
    {
        return _connectionStatus;
    }

    public Task<IEnumerable<OpcDataItem>> GetAllItemsAsync()
    {
        return Task.FromResult<IEnumerable<OpcDataItem>>(_items.Values.ToList());
    }

    public Task<OpcServerStatisticsDto> GetStatisticsAsync()
    {
        var uptime = DateTime.UtcNow - _startTime;
        var updatesPerSecond = uptime.TotalSeconds > 0 ? _totalUpdates / uptime.TotalSeconds : 0;

        var result = new OpcServerStatisticsDto
        {
            UptimeMs = (long)uptime.TotalMilliseconds,
            TotalUpdates = _totalUpdates,
            UpdatesPerSecond = updatesPerSecond,
            ConnectionErrors = _connectionErrors,
            LastError = _lastError,
            ActiveGroups = _groups.Keys.ToList()
        };

        return Task.FromResult(result);
    }

    public Task StartMonitoringAsync(CancellationToken cancellationToken = default)
    {
        //_logger.LogInformation("Iniciando monitoramento de dados OPC (modo simulação)");

        // Iniciar timer de simulação para atualizar valores
        //_simulationTimer = new Timer(SimulateDataUpdates, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(2));
// Verificar se estamos em modo de simulação
    bool simulationMode = _opcServer == null || _config?.EnableSimulation == true;

    if (simulationMode)
    {
        _logger.LogInformation("Iniciando monitoramento em modo de simulação");
        // Iniciar timer de simulação para atualizar valores
        _simulationTimer = new Timer(SimulateDataUpdates, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(2));
    }
    else
    {
        _logger.LogInformation("Iniciando monitoramento com servidor OPC real");
        // Ativar todas as assinaturas
        foreach (var subscription in _groups.Values)
        {
            try
            {
                ((Opc.Da.Subscription)subscription).SetEnabled(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro ao ativar assinatura");
            }
        }
    }
        return Task.CompletedTask;
    }

    public Task StopMonitoringAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Parando monitoramento de dados OPC");

        // Parar timer de simulação
        _simulationTimer?.Dispose();
        _simulationTimer = null;

        return Task.CompletedTask;
    }

    private void SimulateDataUpdates(object? state)
    {
        try
        {
            _logger.LogDebug("Iniciando ciclo de simulação de dados. Total de itens: {ItemCount}", _items.Count);
            
            if (_items.Count == 0)
            {
                _logger.LogWarning("Nenhum item para simular. Verifique se os itens foram adicionados corretamente.");
                return;
            }
            
            lock (_lockObject)
            {
                foreach (var item in _items.Values.ToList())
                {
                    try
                    {
                        // Simular diferentes tipos de dados baseados no nome do tag
                        var oldValue = item.Value;
                        var oldQuality = item.Quality;

                        if (item.TagName.Contains("Random"))
                        {
                            if (item.TagName.Contains("Boolean"))
                            {
                                item.Value = _random.Next(0, 2) == 1;
                            }
                            else if (item.TagName.Contains("Real"))
                            {
                                item.Value = _random.NextDouble() * 100;
                            }
                            else if (item.TagName.Contains("Int"))
                            {
                                item.Value = _random.Next(0, 1000);
                            }
                        }
                        else if (item.TagName.Contains("Triangle"))
                        {
                            // Onda triangular
                            var time = DateTime.Now.Millisecond / 1000.0;
                            item.Value = Math.Abs((time % 2) - 1) * 100;
                        }
                        else if (item.TagName.Contains("Saw"))
                        {
                            // Onda dente de serra
                            var time = DateTime.Now.Millisecond / 1000.0;
                            item.Value = (time % 1) * 100;
                        }
                        else if (item.TagName.Contains("Square"))
                        {
                            // Onda quadrada
                            var time = DateTime.Now.Second;
                            item.Value = (time % 4) < 2;
                        }
                        else if (item.TagName.Contains("Brigade"))
                        {
                            // Sequência ordenada
                            var time = DateTime.Now.Second;
                            item.Value = (time % 10) * 10;
                        }
                        else
                        {
                            // Para tags que não correspondem a nenhum padrão, gerar valor aleatório
                            // para garantir que todos os itens sejam atualizados
                            item.Value = _random.NextDouble() * 100;
                        }

                        item.Timestamp = DateTime.UtcNow;
                        item.Quality = OpcQuality.Good;

                        // Sempre disparar o evento para fins de depuração
                        _totalUpdates++;
                        DataUpdated?.Invoke(this, new OpcDataUpdateEventArgs(item));
                        
                        _logger.LogDebug("Simulação para item {TagName}: Valor={Value}, Antigo={OldValue}, Mudou={Changed}", 
                            item.TagName, item.Value, oldValue, !Equals(oldValue, item.Value));
                    }
                    catch (Exception itemEx)
                    {
                        _logger.LogWarning(itemEx, "Erro ao simular dados para item {TagName}", item.TagName);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro na simulação de dados OPC");
        }
    }

    private void SetConnectionStatus(OpcConnectionStatus status, string? message = null)
    {
        if (_connectionStatus != status)
        {
            _connectionStatus = status;
            ConnectionStatusChanged?.Invoke(this, new AssetView.Nx.Modules.OpcDa.Interfaces.OpcConnectionStatusEventArgs(status, message));
            _logger.LogInformation("Status da conexão alterado para: {Status}", status);
        }
    }

    private void ReconnectCallback(object? state)
    {
        // if (_config != null && _reconnectAttempts < _config.MaxReconnectAttempts)
        // {
        //     _reconnectAttempts++;
        //     _logger.LogInformation("Tentativa de reconexão {Attempt}/{Max}", _reconnectAttempts, _config.MaxReconnectAttempts);

        //     Task.Run(async () =>
        //     {
        //         var success = await ConnectAsync(_config);
        //         if (!success && _reconnectAttempts < _config.MaxReconnectAttempts)
        //         {
        //             _reconnectTimer.Change(_config.ReconnectInterval, Timeout.Infinite);
        //         }
        //     });
        // }
        if (_config != null && _reconnectAttempts < _config.MaxReconnectAttempts)
        {
            _reconnectAttempts++;
            _logger.LogInformation("Tentativa de reconexão {Attempt}/{Max}", 
                _reconnectAttempts, _config.MaxReconnectAttempts);

            Task.Run(async () =>
            {
                try
                {
                    // Salvar configurações de grupos atuais
                    var existingGroups = _groups.Keys.ToList();
                    var existingItems = _items.Values.ToList();
                    
                    // Limpar grupos e itens atuais
                    _groups.Clear();
                    _items.Clear();
                    
                    // Reconectar
                    var success = await ConnectAsync(_config);
                    
                    if (success)
                    {
                        // Restaurar grupos e itens
                        foreach (var groupName in existingGroups)
                        {
                            var groupItems = existingItems.Where(i => i.GroupName == groupName).ToList();
                            var group = new OpcGroup
                            {
                                Name = groupName,
                                IsActive = true,
                                UpdateRate = 1000, // Usar valor padrão ou armazenar configuração
                                Items = groupItems
                            };
                            
                            await AddGroupAsync(group);
                        }
                        
                        // Reiniciar monitoramento
                        await StartMonitoringAsync();
                    }
                    else if (_reconnectAttempts < _config.MaxReconnectAttempts)
                    {
                        _reconnectTimer.Change(_config.ReconnectInterval, Timeout.Infinite);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Erro durante tentativa de reconexão");
                    if (_reconnectAttempts < _config.MaxReconnectAttempts)
                    {
                        _reconnectTimer.Change(_config.ReconnectInterval, Timeout.Infinite);
                    }
                }
            });
        }
        else
        {
            _logger.LogWarning("Número máximo de tentativas de reconexão atingido");
        }
    }

    public void Dispose()
    {
        try
        {
            DisconnectAsync().Wait();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro durante dispose do OpcDaService");
        }
        finally
        {
            _reconnectTimer?.Dispose();
            _simulationTimer?.Dispose();
            _factory?.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
