using AssetView.Nx.Core.Entities;
using AssetView.Nx.Core.Interfaces;

namespace AssetView.Nx.Infrastructure.Repositories;

/// <summary>
/// Interface para repositório de tokens de reset de senha
/// </summary>
public interface IPasswordResetTokenRepository : ITenantRepository<PasswordResetToken>
{
    /// <summary>
    /// Busca um token válido pelo valor do token
    /// </summary>
    /// <param name="token">Token a ser buscado</param>
    /// <returns>Token se encontrado e válido</returns>
    Task<PasswordResetToken?> GetValidTokenAsync(string token);

    /// <summary>
    /// Busca tokens válidos para um usuário
    /// </summary>
    /// <param name="userId">ID do usuário</param>
    /// <returns>Lista de tokens válidos</returns>
    Task<IEnumerable<PasswordResetToken>> GetValidTokensByUserIdAsync(string userId);

    /// <summary>
    /// Invalida todos os tokens de um usuário
    /// </summary>
    /// <param name="userId">ID do usuário</param>
    /// <returns>Número de tokens invalidados</returns>
    Task<int> InvalidateUserTokensAsync(string userId);

    /// <summary>
    /// Remove tokens expirados
    /// </summary>
    /// <returns>Número de tokens removidos</returns>
    Task<int> CleanupExpiredTokensAsync();

    /// <summary>
    /// Conta quantos tokens válidos um usuário possui
    /// </summary>
    /// <param name="userId">ID do usuário</param>
    /// <returns>Número de tokens válidos</returns>
    Task<int> CountValidTokensByUserIdAsync(string userId);

    /// <summary>
    /// Busca tokens por endereço IP (para rate limiting)
    /// </summary>
    /// <param name="ipAddress">Endereço IP</param>
    /// <param name="hoursBack">Horas para trás na busca</param>
    /// <returns>Lista de tokens</returns>
    Task<IEnumerable<PasswordResetToken>> GetTokensByIpAddressAsync(string ipAddress, int hoursBack = 24);
}
