using AssetView.Nx.Core.Entities;
using AssetView.Nx.Core.Interfaces;
using AssetView.Nx.Infrastructure.Services;
using AssetView.Nx.Infrastructure.Services.Email;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;

namespace AssetView.Nx.Modules.Auth.Services;

/// <summary>
/// Implementação do serviço de autenticação
/// </summary>
public class AuthService : IAuthService
{
    private readonly ITenantRepository<ApplicationUser> _userRepository;
    private readonly ITenantRepository<ApplicationRole> _roleRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ITenantService _tenantService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILogger<AuthService> _logger;
    private readonly IPasswordResetTokenService _tokenService;
    private readonly IEmailService _emailService;

    public AuthService(
        ITenantRepository<ApplicationUser> userRepository,
        ITenantRepository<ApplicationRole> roleRepository,
        IUnitOfWork unitOfWork,
        ITenantService tenantService,
        IHttpContextAccessor httpContextAccessor,
        ILogger<AuthService> logger,
        IPasswordResetTokenService tokenService,
        IEmailService emailService)
    {
        _userRepository = userRepository;
        _roleRepository = roleRepository;
        _unitOfWork = unitOfWork;
        _tenantService = tenantService;
        _httpContextAccessor = httpContextAccessor;
        _logger = logger;
        _tokenService = tokenService;
        _emailService = emailService;
    }

    public async Task<AuthResult> RegisterAsync(RegisterRequest request)
    {
        try
        {
            var currentTenantId = _tenantService.GetCurrentTenantId();
            if (string.IsNullOrEmpty(currentTenantId))
            {
                return new AuthResult
                {
                    Success = false,
                    Message = "Tenant não identificado"
                };
            }

            // Verificar se email já existe
            var existingUser = await _userRepository.GetFirstOrDefaultAsync(u => u.Email == request.Email);
            if (existingUser != null)
            {
                return new AuthResult
                {
                    Success = false,
                    Message = "Email já está em uso"
                };
            }

            // Verificar se username já existe
            existingUser = await _userRepository.GetFirstOrDefaultAsync(u => u.UserName == request.UserName);
            if (existingUser != null)
            {
                return new AuthResult
                {
                    Success = false,
                    Message = "Nome de usuário já está em uso"
                };
            }

            // Criar novo usuário
            var user = new ApplicationUser
            {
                TenantId = currentTenantId,
                FullName = request.FullName,
                Email = request.Email,
                UserName = request.UserName,
                PhoneNumber = request.PhoneNumber,
                PasswordHash = HashPassword(request.Password),
                EmailConfirmed = false,
                IsActive = true
            };

            await _userRepository.AddAsync(user);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Usuário {Email} registrado com sucesso no tenant {TenantId}", 
                request.Email, currentTenantId);

            return new AuthResult
            {
                Success = true,
                Message = "Usuário registrado com sucesso",
                User = user
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao registrar usuário {Email}", request.Email);
            return new AuthResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    public async Task<AuthResult> LoginAsync(LoginRequest request)
    {
        try
        {
            // Buscar usuário por email ou username
            var user = await _userRepository.GetFirstOrDefaultAsync(u => 
                u.Email == request.EmailOrUserName || u.UserName == request.EmailOrUserName);

            if (user == null || !VerifyPassword(request.Password, user.PasswordHash))
            {
                return new AuthResult
                {
                    Success = false,
                    Message = "Credenciais inválidas"
                };
            }

            if (!user.IsActive)
            {
                return new AuthResult
                {
                    Success = false,
                    Message = "Usuário inativo"
                };
            }

            // Verificar 2FA se habilitado
            if (user.TwoFactorEnabled && string.IsNullOrEmpty(request.TwoFactorCode))
            {
                return new AuthResult
                {
                    Success = false,
                    Message = "Código de dois fatores necessário"
                };
            }

            if (user.TwoFactorEnabled && !string.IsNullOrEmpty(request.TwoFactorCode))
            {
                // Aqui implementaríamos a verificação do código 2FA
                // Por simplicidade, vamos aceitar qualquer código de 6 dígitos
                if (request.TwoFactorCode.Length != 6 || !request.TwoFactorCode.All(char.IsDigit))
                {
                    return new AuthResult
                    {
                        Success = false,
                        Message = "Código de dois fatores inválido"
                    };
                }
            }

            // Buscar roles do usuário
            var userRoles = await GetUserRolesAsync(user.Id);

            // Criar claims do usuário
            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, user.Id),
                new(ClaimTypes.Name, user.UserName),
                new(ClaimTypes.Email, user.Email),
                new("FullName", user.FullName),
                new("TenantId", user.TenantId)
            };

            // Adicionar roles como claims
            foreach (var role in userRoles)
            {
                claims.Add(new Claim(ClaimTypes.Role, role.Name));
            }

            // Criar identidade e principal
            var identity = new ClaimsIdentity(claims, "Cookies");
            var principal = new ClaimsPrincipal(identity);

            // Fazer sign in
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext != null)
            {
                await httpContext.SignInAsync("Cookies", principal);
            }

            // Atualizar último login
            user.LastLoginAt = DateTime.UtcNow;
            await _userRepository.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Usuário {Email} fez login com sucesso", user.Email);

            return new AuthResult
            {
                Success = true,
                Message = "Login realizado com sucesso",
                User = user
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao fazer login do usuário {EmailOrUserName}", request.EmailOrUserName);
            return new AuthResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    public async Task LogoutAsync(string userId)
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext != null)
            {
                await httpContext.SignOutAsync("Cookies");
            }

            _logger.LogInformation("Usuário {UserId} fez logout", userId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao fazer logout do usuário {UserId}", userId);
        }
    }

    public async Task<AuthResult> ConfirmEmailAsync(string userId, string token)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                return new AuthResult
                {
                    Success = false,
                    Message = "Usuário não encontrado"
                };
            }

            // Aqui implementaríamos a verificação do token
            // Por simplicidade, vamos aceitar qualquer token
            user.EmailConfirmed = true;
            await _userRepository.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            return new AuthResult
            {
                Success = true,
                Message = "Email confirmado com sucesso"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao confirmar email do usuário {UserId}", userId);
            return new AuthResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    public async Task<AuthResult> ForgotPasswordAsync(string email)
    {
        try
        {
            var user = await _userRepository.GetFirstOrDefaultAsync(u => u.Email == email);
            if (user == null)
            {
                // Por segurança, sempre retornamos sucesso mesmo se o email não existir
                _logger.LogWarning("Tentativa de recuperação de senha para email inexistente: {Email}", email);
                return new AuthResult
                {
                    Success = true,
                    Message = "Se o email existir, um link de recuperação será enviado"
                };
            }

            // Verificar rate limiting
            if (!await _tokenService.CanRequestTokenAsync(user.Id))
            {
                _logger.LogWarning("Rate limiting ativado para usuário {UserId} ({Email})", user.Id, email);
                return new AuthResult
                {
                    Success = false,
                    Message = "Muitas solicitações de recuperação. Tente novamente mais tarde."
                };
            }

            // Obter informações da requisição
            var httpContext = _httpContextAccessor.HttpContext;
            var ipAddress = httpContext?.Connection?.RemoteIpAddress?.ToString();
            var userAgent = httpContext?.Request?.Headers["User-Agent"].ToString();

            // Verificar rate limiting por IP
            if (!string.IsNullOrEmpty(ipAddress) && !await _tokenService.CanRequestTokenByIpAsync(ipAddress))
            {
                _logger.LogWarning("Rate limiting por IP ativado para {IpAddress}", ipAddress);
                return new AuthResult
                {
                    Success = false,
                    Message = "Muitas solicitações deste endereço IP. Tente novamente mais tarde."
                };
            }

            // Gerar token de reset
            var token = await _tokenService.GenerateTokenAsync(user.Id, ipAddress, userAgent);

            // Criar link de reset
            var baseUrl = httpContext?.Request?.Scheme + "://" + httpContext?.Request?.Host;
            var resetLink = $"{baseUrl}/reset-password?token={token.Token}";

            // Enviar email de recuperação
            var emailResult = await _emailService.SendPasswordResetEmailAsync(user.Email, user.UserName, resetLink);

            if (!emailResult.Success)
            {
                _logger.LogError("Falha ao enviar email de recuperação para {Email}: {Error}", email, emailResult.Message);
                return new AuthResult
                {
                    Success = false,
                    Message = "Erro ao enviar email de recuperação. Tente novamente mais tarde."
                };
            }

            _logger.LogInformation("Email de recuperação enviado com sucesso para {Email}", email);

            return new AuthResult
            {
                Success = true,
                Message = "Se o email existir, um link de recuperação será enviado"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao processar recuperação de senha para {Email}", email);
            return new AuthResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    public async Task<AuthResult> ResetPasswordAsync(ResetPasswordRequest request)
    {
        try
        {
            // Validar token primeiro
            var tokenEntity = await _tokenService.ValidateTokenAsync(request.Token);
            if (tokenEntity == null)
            {
                _logger.LogWarning("Tentativa de reset com token inválido ou expirado");
                return new AuthResult
                {
                    Success = false,
                    Message = "Token inválido ou expirado"
                };
            }

            // Buscar usuário pelo ID do token (mais seguro que pelo email)
            var user = await _userRepository.GetByIdAsync(tokenEntity.UserId);
            if (user == null)
            {
                _logger.LogError("Usuário não encontrado para token válido: {UserId}", tokenEntity.UserId);
                return new AuthResult
                {
                    Success = false,
                    Message = "Usuário não encontrado"
                };
            }

            // Verificar se o email do request corresponde ao usuário do token
            if (!string.Equals(user.Email, request.Email, StringComparison.OrdinalIgnoreCase))
            {
                _logger.LogWarning("Tentativa de reset com email incorreto. Token UserId: {UserId}, Email solicitado: {Email}",
                    tokenEntity.UserId, request.Email);
                return new AuthResult
                {
                    Success = false,
                    Message = "Dados inválidos"
                };
            }

            // Obter informações da requisição
            var httpContext = _httpContextAccessor.HttpContext;
            var ipAddress = httpContext?.Connection?.RemoteIpAddress?.ToString();
            var userAgent = httpContext?.Request?.Headers["User-Agent"].ToString();

            // Consumir o token (marca como usado)
            var tokenConsumed = await _tokenService.ConsumeTokenAsync(request.Token, ipAddress, userAgent);
            if (!tokenConsumed)
            {
                _logger.LogError("Falha ao consumir token para usuário {UserId}", user.Id);
                return new AuthResult
                {
                    Success = false,
                    Message = "Erro ao processar token"
                };
            }

            // Invalidar todos os outros tokens do usuário por segurança
            await _tokenService.InvalidateUserTokensAsync(user.Id);

            // Alterar a senha
            user.PasswordHash = HashPassword(request.NewPassword);
            user.UpdatedAt = DateTime.UtcNow;

            await _userRepository.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Senha resetada com sucesso para usuário {UserId} ({Email})", user.Id, user.Email);

            return new AuthResult
            {
                Success = true,
                Message = "Senha alterada com sucesso"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao resetar senha para {Email}", request.Email);
            return new AuthResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    public async Task<AuthResult> ValidateResetTokenAsync(string token)
    {
        try
        {
            // Validar token
            var tokenEntity = await _tokenService.ValidateTokenAsync(token);
            if (tokenEntity == null)
            {
                return new AuthResult
                {
                    Success = false,
                    Message = "Token inválido ou expirado"
                };
            }

            // Verificar se o usuário ainda existe
            var user = await _userRepository.GetByIdAsync(tokenEntity.UserId);
            if (user == null)
            {
                _logger.LogError("Usuário não encontrado para token válido: {UserId}", tokenEntity.UserId);
                return new AuthResult
                {
                    Success = false,
                    Message = "Usuário não encontrado"
                };
            }

            _logger.LogInformation("Token validado com sucesso para usuário {UserId}", user.Id);

            return new AuthResult
            {
                Success = true,
                Message = "Token válido",
                User = user
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao validar token de reset");
            return new AuthResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    public async Task<AuthResult> ChangePasswordAsync(string userId, string currentPassword, string newPassword)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                return new AuthResult
                {
                    Success = false,
                    Message = "Usuário não encontrado"
                };
            }

            if (!VerifyPassword(currentPassword, user.PasswordHash))
            {
                return new AuthResult
                {
                    Success = false,
                    Message = "Senha atual incorreta"
                };
            }

            user.PasswordHash = HashPassword(newPassword);
            await _userRepository.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            return new AuthResult
            {
                Success = true,
                Message = "Senha alterada com sucesso"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao alterar senha do usuário {UserId}", userId);
            return new AuthResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    public async Task<TwoFactorResult> EnableTwoFactorAsync(string userId)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                return new TwoFactorResult
                {
                    Success = false,
                    Message = "Usuário não encontrado"
                };
            }

            user.TwoFactorEnabled = true;
            await _userRepository.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            // Aqui implementaríamos a geração do QR Code e chave compartilhada
            return new TwoFactorResult
            {
                Success = true,
                Message = "Autenticação de dois fatores habilitada",
                SharedKey = "EXEMPLO_CHAVE_COMPARTILHADA",
                QrCodeUri = "otpauth://totp/AssetView.Nx.System?secret=EXEMPLO_CHAVE_COMPARTILHADA"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao habilitar 2FA para usuário {UserId}", userId);
            return new TwoFactorResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    public async Task<AuthResult> DisableTwoFactorAsync(string userId)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                return new AuthResult
                {
                    Success = false,
                    Message = "Usuário não encontrado"
                };
            }

            user.TwoFactorEnabled = false;
            await _userRepository.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            return new AuthResult
            {
                Success = true,
                Message = "Autenticação de dois fatores desabilitada"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao desabilitar 2FA para usuário {UserId}", userId);
            return new AuthResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    public async Task<AuthResult> VerifyTwoFactorAsync(string userId, string code)
    {
        try
        {
            // Aqui implementaríamos a verificação real do código 2FA
            // Por simplicidade, vamos aceitar qualquer código de 6 dígitos
            if (code.Length == 6 && code.All(char.IsDigit))
            {
                return new AuthResult
                {
                    Success = true,
                    Message = "Código verificado com sucesso"
                };
            }

            return new AuthResult
            {
                Success = false,
                Message = "Código inválido"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao verificar código 2FA para usuário {UserId}", userId);
            return new AuthResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    public async Task<ApplicationUser?> GetCurrentUserAsync()
    {
        try
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.User?.Identity?.IsAuthenticated == true)
            {
                var userId = httpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!string.IsNullOrEmpty(userId))
                {
                    return await _userRepository.GetByIdAsync(userId);
                }
            }
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter usuário atual");
            return null;
        }
    }

    private async Task<List<ApplicationRole>> GetUserRolesAsync(string userId)
    {
        try
        {
            // Buscar roles do usuário através da tabela UserRoles
            var userRoles = await _userRepository.GetDbContext().Set<UserRole>()
                .Where(ur => ur.UserId == userId)
                .Include(ur => ur.Role)
                .Select(ur => ur.Role)
                .Where(r => r.IsActive && !r.IsDeleted)
                .ToListAsync();

            return userRoles;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar roles do usuário {UserId}", userId);
            return new List<ApplicationRole>();
        }
    }

    private string HashPassword(string password)
    {
        // Implementação simples de hash - em produção usar BCrypt ou similar
        using var sha256 = SHA256.Create();
        var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "SALT"));
        return Convert.ToBase64String(hashedBytes);
    }

    private bool VerifyPassword(string password, string hash)
    {
        return HashPassword(password) == hash;
    }
}
