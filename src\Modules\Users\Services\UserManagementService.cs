using AssetView.Nx.Core.Entities;
using AssetView.Nx.Core.Interfaces;
using AssetView.Nx.Shared.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using System.Text;

namespace AssetView.Nx.Modules.Users.Services;

/// <summary>
/// Implementação do serviço de gestão de usuários
/// </summary>
public class UserManagementService : IUserManagementService
{
    private readonly ITenantRepository<ApplicationUser> _userRepository;
    private readonly ITenantRepository<ApplicationRole> _roleRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ITenantService _tenantService;
    private readonly ILogger<UserManagementService> _logger;

    public UserManagementService(
        ITenantRepository<ApplicationUser> userRepository,
        ITenantRepository<ApplicationRole> roleRepository,
        IUnitOfWork unitOfWork,
        ITenantService tenantService,
        ILogger<UserManagementService> logger)
    {
        _userRepository = userRepository;
        _roleRepository = roleRepository;
        _unitOfWork = unitOfWork;
        _tenantService = tenantService;
        _logger = logger;
    }

    public async Task<IEnumerable<ApplicationUser>> GetUsersAsync()
    {
        return await _userRepository.GetAllAsync();
    }

    public async Task<PagedResult<ApplicationUser>> GetUsersPagedAsync(int page, int pageSize, string? searchTerm = null)
    {
        try
        {
            var (users, totalCount) = await _userRepository.GetPagedAsync(
                page,
                pageSize,
                predicate: string.IsNullOrEmpty(searchTerm) ? null : 
                    u => u.FullName.Contains(searchTerm) || u.Email.Contains(searchTerm) || u.UserName.Contains(searchTerm),
                orderBy: u => u.FullName
            );

            return new PagedResult<ApplicationUser>
            {
                Items = users,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter usuários paginados");
            return new PagedResult<ApplicationUser>();
        }
    }

    public async Task<ApplicationUser?> GetUserByIdAsync(string userId)
    {
        return await _userRepository.GetByIdAsync(userId);
    }

    public async Task<UserResult> CreateUserAsync(CreateUserRequest request)
    {
        try
        {
            // Validar se email já existe
            var existingUser = await _userRepository.GetFirstOrDefaultAsync(u => u.Email == request.Email);
            if (existingUser != null)
            {
                return new UserResult
                {
                    Success = false,
                    Message = "Email já está em uso"
                };
            }

            // Validar se username já existe
            existingUser = await _userRepository.GetFirstOrDefaultAsync(u => u.UserName == request.UserName);
            if (existingUser != null)
            {
                return new UserResult
                {
                    Success = false,
                    Message = "Nome de usuário já está em uso"
                };
            }

            var user = new ApplicationUser
            {
                FullName = request.FullName,
                Email = request.Email,
                UserName = request.UserName,
                PhoneNumber = request.PhoneNumber,
                PasswordHash = HashPassword(request.Password),
                IsActive = request.IsActive,
                EmailConfirmed = false
            };

            await _userRepository.AddAsync(user);
            await _unitOfWork.SaveChangesAsync();

            // Atribuir roles ao usuário
            if (request.RoleIds != null && request.RoleIds.Any())
            {
                await AssignRolesToUserAsync(user.Id, request.RoleIds);
            }

            _logger.LogInformation("Usuário {Email} criado com sucesso", request.Email);

            return new UserResult
            {
                Success = true,
                Message = "Usuário criado com sucesso",
                User = user
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao criar usuário {Email}", request.Email);
            return new UserResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    public async Task<UserResult> UpdateUserAsync(string userId, UpdateUserRequest request)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                return new UserResult
                {
                    Success = false,
                    Message = "Usuário não encontrado"
                };
            }

            // Validar se email já existe (exceto para o usuário atual)
            var existingUser = await _userRepository.GetFirstOrDefaultAsync(u => u.Email == request.Email && u.Id != userId);
            if (existingUser != null)
            {
                return new UserResult
                {
                    Success = false,
                    Message = "Email já está em uso"
                };
            }

            // Validar se username já existe (exceto para o usuário atual)
            existingUser = await _userRepository.GetFirstOrDefaultAsync(u => u.UserName == request.UserName && u.Id != userId);
            if (existingUser != null)
            {
                return new UserResult
                {
                    Success = false,
                    Message = "Nome de usuário já está em uso"
                };
            }

            user.FullName = request.FullName;
            user.Email = request.Email;
            user.UserName = request.UserName;
            user.PhoneNumber = request.PhoneNumber;
            user.IsActive = request.IsActive;

            await _userRepository.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            // Atualizar roles do usuário
            if (request.RoleIds != null)
            {
                await UpdateUserRolesAsync(userId, request.RoleIds);
            }

            _logger.LogInformation("Usuário {UserId} atualizado com sucesso", userId);

            return new UserResult
            {
                Success = true,
                Message = "Usuário atualizado com sucesso",
                User = user
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao atualizar usuário {UserId}", userId);
            return new UserResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    public async Task<UserResult> ToggleUserStatusAsync(string userId)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                return new UserResult
                {
                    Success = false,
                    Message = "Usuário não encontrado"
                };
            }

            user.IsActive = !user.IsActive;
            await _userRepository.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            var status = user.IsActive ? "ativado" : "desativado";
            _logger.LogInformation("Usuário {UserId} {Status}", userId, status);

            return new UserResult
            {
                Success = true,
                Message = $"Usuário {status} com sucesso",
                User = user
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao alterar status do usuário {UserId}", userId);
            return new UserResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    public async Task<UserResult> DeleteUserAsync(string userId)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                return new UserResult
                {
                    Success = false,
                    Message = "Usuário não encontrado"
                };
            }

            user.IsDeleted = true;
            user.DeletedAt = DateTime.UtcNow;
            await _userRepository.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            _logger.LogInformation("Usuário {UserId} excluído com sucesso", userId);

            return new UserResult
            {
                Success = true,
                Message = "Usuário excluído com sucesso"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao excluir usuário {UserId}", userId);
            return new UserResult
            {
                Success = false,
                Message = "Erro interno do servidor"
            };
        }
    }

    public async Task<IEnumerable<ApplicationRole>> GetUserRolesAsync(string userId)
    {
        try
        {
            var userRoles = await _userRepository.GetDbContext().Set<UserRole>()
                .Where(ur => ur.UserId == userId)
                .Include(ur => ur.Role)
                .Select(ur => ur.Role)
                .Where(r => r.IsActive && !r.IsDeleted)
                .ToListAsync();

            return userRoles;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar roles do usuário {UserId}", userId);
            return new List<ApplicationRole>();
        }
    }

    public async Task<UserResult> AddUserToRoleAsync(string userId, string roleId)
    {
        // Implementação simplificada
        return new UserResult
        {
            Success = true,
            Message = "Role adicionado ao usuário com sucesso"
        };
    }

    public async Task<UserResult> RemoveUserFromRoleAsync(string userId, string roleId)
    {
        // Implementação simplificada
        return new UserResult
        {
            Success = true,
            Message = "Role removido do usuário com sucesso"
        };
    }

    public async Task<IEnumerable<ApplicationRole>> GetAvailableRolesAsync()
    {
        return await _roleRepository.GetAllAsync();
    }

    private async Task AssignRolesToUserAsync(string userId, List<string> roleIds)
    {
        try
        {
            var dbContext = _userRepository.GetDbContext();

            foreach (var roleId in roleIds)
            {
                var userRole = new UserRole
                {
                    UserId = userId,
                    RoleId = roleId,
                    CreatedAt = DateTime.UtcNow
                };

                dbContext.Set<UserRole>().Add(userRole);
            }

            await _unitOfWork.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao atribuir roles ao usuário {UserId}", userId);
            throw;
        }
    }

    private async Task UpdateUserRolesAsync(string userId, List<string> roleIds)
    {
        try
        {
            var dbContext = _userRepository.GetDbContext();

            // Remover todas as roles existentes do usuário
            var existingUserRoles = await dbContext.Set<UserRole>()
                .Where(ur => ur.UserId == userId)
                .ToListAsync();

            dbContext.Set<UserRole>().RemoveRange(existingUserRoles);

            // Adicionar as novas roles
            foreach (var roleId in roleIds)
            {
                var userRole = new UserRole
                {
                    UserId = userId,
                    RoleId = roleId,
                    CreatedAt = DateTime.UtcNow
                };

                dbContext.Set<UserRole>().Add(userRole);
            }

            await _unitOfWork.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao atualizar roles do usuário {UserId}", userId);
            throw;
        }
    }

    private string HashPassword(string password)
    {
        using var sha256 = SHA256.Create();
        var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "SALT"));
        return Convert.ToBase64String(hashedBytes);
    }
}
