{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Services\\OpcDa\\AssetView.Nx.OpcDa.Service.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Core\\AssetView.Nx.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Core\\AssetView.Nx.Core.csproj", "projectName": "AssetView.Nx.Core", "projectPath": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Core\\AssetView.Nx.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\NuGetPackages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Identity": {"target": "Package", "version": "[2.3.1, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.6, )"}, "Nanoid": {"target": "Package", "version": "[3.1.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Modules\\OpcDa\\AssetView.Nx.Modules.OpcDa.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Modules\\OpcDa\\AssetView.Nx.Modules.OpcDa.csproj", "projectName": "AssetView.Nx.Modules.OpcDa", "projectPath": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Modules\\OpcDa\\AssetView.Nx.Modules.OpcDa.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Modules\\OpcDa\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\NuGetPackages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Core\\AssetView.Nx.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Core\\AssetView.Nx.Core.csproj"}, "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Shared\\AssetView.Nx.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Shared\\AssetView.Nx.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"Microsoft.AspNetCore.SignalR.Client": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.6, )"}, "OpcNetApi": {"target": "Package", "version": "[2.1.109, )"}, "OpcNetApi.Com": {"target": "Package", "version": "[2.1.109, )"}, "OpcNetApi.Xml": {"target": "Package", "version": "[2.1.109, )"}, "System.Runtime.InteropServices": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Services\\OpcDa\\AssetView.Nx.OpcDa.Service.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Services\\OpcDa\\AssetView.Nx.OpcDa.Service.csproj", "projectName": "AssetView.Nx.OpcDa.Service", "projectPath": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Services\\OpcDa\\AssetView.Nx.OpcDa.Service.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Services\\OpcDa\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\NuGetPackages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Core\\AssetView.Nx.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Core\\AssetView.Nx.Core.csproj"}, "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Modules\\OpcDa\\AssetView.Nx.Modules.OpcDa.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Modules\\OpcDa\\AssetView.Nx.Modules.OpcDa.csproj"}, "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Shared\\AssetView.Nx.Shared.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Shared\\AssetView.Nx.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"Microsoft.AspNetCore.SignalR.Client": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.Extensions.Hosting.WindowsServices": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}, "System.Runtime.InteropServices": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Shared\\AssetView.Nx.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Shared\\AssetView.Nx.Shared.csproj", "projectName": "AssetView.Nx.Shared", "projectPath": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Shared\\AssetView.Nx.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\src\\Shared\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\NuGet.Config", "C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Users\\<USER>\\Documents\\Smar.AssetView.Nx\\NuGetPackages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}