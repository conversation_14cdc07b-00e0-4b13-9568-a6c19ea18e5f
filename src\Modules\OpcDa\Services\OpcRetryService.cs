using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using AssetView.Nx.Modules.OpcDa.Interfaces;
using AssetView.Nx.Modules.OpcDa.Models;
using System.Collections.Concurrent;
using Opc;

namespace AssetView.Nx.Modules.OpcDa.Services;

/// <summary>
/// Serviço para retry automático de operações OPC com tratamento robusto de erros
/// </summary>
public class OpcRetryService : IOpcRetryService
{
    private readonly ILogger<OpcRetryService> _logger;
    private readonly OpcRetryOptions _options;
    private readonly OpcErrorHandlingService _errorHandler;
    private readonly ConcurrentDictionary<string, OpcRetryAttempt> _retryAttempts = new();

    public event EventHandler<OpcRetryEventArgs>? RetryAttemptStarted;
    public event EventHandler<OpcRetryEventArgs>? RetryAttemptCompleted;
    public event EventHandler<OpcRetryEventArgs>? RetryExhausted;

    public OpcRetryService(
        ILogger<OpcRetryService> logger,
        IOptions<OpcRetryOptions> options,
        OpcErrorHandlingService errorHandler)
    {
        _logger = logger;
        _options = options.Value;
        _errorHandler = errorHandler;
    }

    /// <summary>
    /// Executa operação com retry automático
    /// </summary>
    public async Task<T> ExecuteWithRetryAsync<T>(
        Func<CancellationToken, Task<T>> operation,
        OpcOperationType operationType,
        string? operationId = null,
        OpcRetryConfig? customConfig = null,
        CancellationToken cancellationToken = default)
    {
        var opId = operationId ?? Guid.NewGuid().ToString();
        var config = customConfig ?? GetRetryConfigForOperation(operationType);
        
        if (!config.Enabled || !config.RetryableOperations.Contains(operationType))
        {
            // Executar sem retry se não configurado
            return await operation(cancellationToken);
        }

        var retryAttempt = new OpcRetryAttempt
        {
            OperationId = opId,
            OperationType = operationType,
            Config = config,
            StartTime = DateTime.Now,
            MaxAttempts = config.MaxAttempts
        };

        _retryAttempts.TryAdd(opId, retryAttempt);

        try
        {
            return await ExecuteWithRetryInternal(operation, retryAttempt, cancellationToken);
        }
        finally
        {
            // Remover tentativa após um tempo para manter histórico
            _ = Task.Delay(TimeSpan.FromMinutes(5)).ContinueWith(_ =>
                _retryAttempts.TryRemove(opId, out var _));
        }
    }

    /// <summary>
    /// Executa operação sem retorno com retry
    /// </summary>
    public async Task ExecuteWithRetryAsync(
        Func<CancellationToken, Task> operation,
        OpcOperationType operationType,
        string? operationId = null,
        OpcRetryConfig? customConfig = null,
        CancellationToken cancellationToken = default)
    {
        await ExecuteWithRetryAsync(async ct =>
        {
            await operation(ct);
            return true; // Dummy return value
        }, operationType, operationId, customConfig, cancellationToken);
    }

    /// <summary>
    /// Obtém configuração de retry para tipo de operação
    /// </summary>
    public OpcRetryConfig GetRetryConfigForOperation(OpcOperationType operationType)
    {
        return operationType switch
        {
            OpcOperationType.Connect => new OpcRetryConfig
            {
                Enabled = _options.EnableConnectionRetry,
                MaxAttempts = _options.ConnectionMaxAttempts,
                InitialDelay = TimeSpan.FromSeconds(_options.ConnectionRetryDelaySeconds),
                BackoffMultiplier = _options.ConnectionBackoffMultiplier,
                RetryableOperations = { OpcOperationType.Connect }
            },
            OpcOperationType.Read => new OpcRetryConfig
            {
                Enabled = _options.EnableReadRetry,
                MaxAttempts = _options.ReadMaxAttempts,
                InitialDelay = TimeSpan.FromSeconds(_options.ReadRetryDelaySeconds),
                BackoffMultiplier = _options.ReadBackoffMultiplier,
                RetryableOperations = { OpcOperationType.Read }
            },
            OpcOperationType.Write => new OpcRetryConfig
            {
                Enabled = _options.EnableWriteRetry,
                MaxAttempts = _options.WriteMaxAttempts,
                InitialDelay = TimeSpan.FromSeconds(_options.WriteRetryDelaySeconds),
                BackoffMultiplier = _options.WriteBackoffMultiplier,
                RetryableOperations = { OpcOperationType.Write }
            },
            OpcOperationType.Discovery => new OpcRetryConfig
            {
                Enabled = _options.EnableDiscoveryRetry,
                MaxAttempts = _options.DiscoveryMaxAttempts,
                InitialDelay = TimeSpan.FromSeconds(_options.DiscoveryRetryDelaySeconds),
                BackoffMultiplier = _options.DiscoveryBackoffMultiplier,
                RetryableOperations = { OpcOperationType.Discovery }
            },
            _ => new OpcRetryConfig
            {
                Enabled = _options.EnableDefaultRetry,
                MaxAttempts = _options.DefaultMaxAttempts,
                InitialDelay = TimeSpan.FromSeconds(_options.DefaultRetryDelaySeconds),
                BackoffMultiplier = _options.DefaultBackoffMultiplier,
                RetryableOperations = { operationType }
            }
        };
    }

    /// <summary>
    /// Obtém estatísticas de retry
    /// </summary>
    public async Task<OpcRetryStatistics> GetRetryStatisticsAsync(CancellationToken cancellationToken = default)
    {
        var attempts = _retryAttempts.Values.ToList();
        var completedAttempts = attempts.Where(a => a.IsCompleted).ToList();

        return new OpcRetryStatistics
        {
            TotalOperations = attempts.Count,
            CompletedOperations = completedAttempts.Count,
            SuccessfulOperations = completedAttempts.Count(a => a.Success),
            FailedOperations = completedAttempts.Count(a => !a.Success),
            TotalRetryAttempts = completedAttempts.Sum(a => a.AttemptCount - 1),
            AverageAttemptsPerOperation = completedAttempts.Any() ? 
                completedAttempts.Average(a => a.AttemptCount) : 0.0,
            SuccessRateAfterRetry = completedAttempts.Any() ? 
                (double)completedAttempts.Count(a => a.Success) / completedAttempts.Count * 100 : 0.0,
            OperationsByType = attempts
                .GroupBy(a => a.OperationType)
                .ToDictionary(g => g.Key, g => g.Count()),
            RetrySuccessRate = CalculateRetrySuccessRate(completedAttempts)
        };
    }

    /// <summary>
    /// Lista tentativas ativas
    /// </summary>
    public async Task<List<OpcActiveRetryAttempt>> GetActiveRetryAttemptsAsync(CancellationToken cancellationToken = default)
    {
        return _retryAttempts.Values
            .Where(a => !a.IsCompleted)
            .Select(a => new OpcActiveRetryAttempt
            {
                OperationId = a.OperationId,
                OperationType = a.OperationType,
                CurrentAttempt = a.AttemptCount,
                MaxAttempts = a.MaxAttempts,
                StartTime = a.StartTime,
                LastAttemptTime = a.LastAttemptTime,
                NextRetryTime = a.NextRetryTime,
                ElapsedTime = DateTime.Now - a.StartTime,
                IsWaitingForRetry = a.IsWaitingForRetry
            })
            .ToList();
    }

    /// <summary>
    /// Cancela tentativas de retry para operação específica
    /// </summary>
    public async Task<bool> CancelRetryAsync(string operationId, CancellationToken cancellationToken = default)
    {
        if (_retryAttempts.TryGetValue(operationId, out var attempt))
        {
            attempt.CancellationTokenSource?.Cancel();
            attempt.IsCompleted = true;
            attempt.CompletedAt = DateTime.Now;
            
            _logger.LogInformation("Retry cancelado para operação: {OperationId}", operationId);
            return true;
        }
        
        return false;
    }

    /// <summary>
    /// Execução interna com retry
    /// </summary>
    private async Task<T> ExecuteWithRetryInternal<T>(
        Func<CancellationToken, Task<T>> operation,
        OpcRetryAttempt retryAttempt,
        CancellationToken cancellationToken)
    {
        var config = retryAttempt.Config;
        var currentDelay = config.InitialDelay;
        Exception? lastException = null;

        for (int attempt = 1; attempt <= config.MaxAttempts; attempt++)
        {
            retryAttempt.AttemptCount = attempt;
            retryAttempt.LastAttemptTime = DateTime.Now;

            try
            {
                _logger.LogDebug("Tentativa {Attempt}/{MaxAttempts} para operação {OperationType}. ID: {OperationId}", 
                    attempt, config.MaxAttempts, retryAttempt.OperationType, retryAttempt.OperationId);

                RetryAttemptStarted?.Invoke(this, new OpcRetryEventArgs
                {
                    OperationId = retryAttempt.OperationId,
                    OperationType = retryAttempt.OperationType,
                    AttemptNumber = attempt,
                    MaxAttempts = config.MaxAttempts,
                    IsRetry = attempt > 1
                });

                var result = await operation(cancellationToken);
                
                // Sucesso
                retryAttempt.Success = true;
                retryAttempt.IsCompleted = true;
                retryAttempt.CompletedAt = DateTime.Now;

                RetryAttemptCompleted?.Invoke(this, new OpcRetryEventArgs
                {
                    OperationId = retryAttempt.OperationId,
                    OperationType = retryAttempt.OperationType,
                    AttemptNumber = attempt,
                    MaxAttempts = config.MaxAttempts,
                    Success = true,
                    Duration = retryAttempt.Duration
                });

                if (attempt > 1)
                {
                    _logger.LogInformation("Operação {OperationType} bem-sucedida na tentativa {Attempt}. ID: {OperationId}", 
                        retryAttempt.OperationType, attempt, retryAttempt.OperationId);
                }

                return result;
            }
            catch (Exception ex)
            {
                lastException = ex;

                // Usar o tratamento robusto de erros
                var errorInfo = _errorHandler.HandleOpcException(ex,
                    $"Operação {retryAttempt.OperationType}",
                    retryAttempt.OperationId);

                retryAttempt.Errors.Add($"Tentativa {attempt}: {errorInfo.Message}");

                // Verificar se deve tentar novamente
                if (attempt < config.MaxAttempts && _errorHandler.ShouldRetry(errorInfo, attempt, config.MaxAttempts))
                {
                    // Calcular delay usando o tratamento de erros
                    var nextDelay = _errorHandler.GetRetryDelay(errorInfo, attempt);

                    // Limitar pelo máximo configurado
                    if (nextDelay > config.MaxDelay)
                    {
                        nextDelay = config.MaxDelay;
                    }

                    retryAttempt.IsWaitingForRetry = true;
                    retryAttempt.NextRetryTime = DateTime.Now.Add(nextDelay);

                    _logger.LogDebug("Aguardando {Delay}ms antes da próxima tentativa", nextDelay.TotalMilliseconds);

                    try
                    {
                        await Task.Delay(nextDelay, cancellationToken);
                    }
                    catch (OperationCanceledException)
                    {
                        retryAttempt.IsCompleted = true;
                        retryAttempt.CompletedAt = DateTime.Now;
                        throw;
                    }

                    retryAttempt.IsWaitingForRetry = false;
                    currentDelay = nextDelay;
                }
                else
                {
                    // Não deve tentar novamente ou última tentativa
                    break;
                }
            }
        }

        // Todas as tentativas falharam
        retryAttempt.Success = false;
        retryAttempt.IsCompleted = true;
        retryAttempt.CompletedAt = DateTime.Now;

        RetryExhausted?.Invoke(this, new OpcRetryEventArgs
        {
            OperationId = retryAttempt.OperationId,
            OperationType = retryAttempt.OperationType,
            AttemptNumber = retryAttempt.AttemptCount,
            MaxAttempts = config.MaxAttempts,
            Success = false,
            Duration = retryAttempt.Duration,
            ErrorMessage = lastException?.Message
        });

        _logger.LogError(lastException, "Operação {OperationType} falhou após {Attempts} tentativas. ID: {OperationId}", 
            retryAttempt.OperationType, retryAttempt.AttemptCount, retryAttempt.OperationId);

        throw lastException ?? new InvalidOperationException("Operação falhou após múltiplas tentativas");
    }

    /// <summary>
    /// Determina se deve tentar novamente baseado na exceção
    /// </summary>
    private static bool ShouldRetry(Exception exception, int attemptNumber, OpcRetryConfig config)
    {
        if (attemptNumber >= config.MaxAttempts)
            return false;

        // Não tentar novamente para OperationCanceledException
        if (exception is OperationCanceledException)
            return false;

        // Não tentar novamente para ArgumentException (erro de configuração)
        if (exception is ArgumentException)
            return false;

        // Tentar novamente para outros tipos de erro
        return true;
    }

    /// <summary>
    /// Calcula taxa de sucesso do retry
    /// </summary>
    private static double CalculateRetrySuccessRate(List<OpcRetryAttempt> completedAttempts)
    {
        var retriedOperations = completedAttempts.Where(a => a.AttemptCount > 1).ToList();
        if (!retriedOperations.Any())
            return 0.0;

        var successfulRetries = retriedOperations.Count(a => a.Success);
        return (double)successfulRetries / retriedOperations.Count * 100;
    }

    public void Dispose()
    {
        // Cancelar todas as tentativas ativas
        foreach (var attempt in _retryAttempts.Values.Where(a => !a.IsCompleted))
        {
            attempt.CancellationTokenSource?.Cancel();
        }
        
        _retryAttempts.Clear();
    }
}
