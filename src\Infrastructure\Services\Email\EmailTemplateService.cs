using System.Reflection;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using AssetView.Nx.Infrastructure.Services.Email.Models;

namespace AssetView.Nx.Infrastructure.Services.Email;

/// <summary>
/// Implementação do serviço de templates de e-mail
/// </summary>
public class EmailTemplateService : IEmailTemplateService
{
    private readonly ILogger<EmailTemplateService> _logger;
    private readonly Dictionary<string, string> _templates;

    public EmailTemplateService(ILogger<EmailTemplateService> logger)
    {
        _logger = logger;
        _templates = new Dictionary<string, string>();
        LoadTemplates();
    }

    public async Task<TemplateResult> RenderTemplateAsync(string templateName, object data)
    {
        try
        {
            if (!_templates.ContainsKey(templateName))
            {
                return TemplateResult.CreateFailure($"Template '{templateName}' não encontrado");
            }

            var template = _templates[templateName];
            var renderedContent = await RenderTemplate(template, data);

            return TemplateResult.CreateSuccess(renderedContent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao renderizar template {TemplateName}", templateName);
            return TemplateResult.CreateFailure($"Erro ao renderizar template: {ex.Message}", ex);
        }
    }

    public async Task<bool> TemplateExistsAsync(string templateName)
    {
        return await Task.FromResult(_templates.ContainsKey(templateName));
    }

    public async Task<IEnumerable<string>> GetAvailableTemplatesAsync()
    {
        return await Task.FromResult(_templates.Keys);
    }

    private void LoadTemplates()
    {
        // Template para recuperação de senha
        _templates["PasswordReset"] = GetPasswordResetTemplate();
        
        // Template para confirmação de conta
        _templates["AccountConfirmation"] = GetAccountConfirmationTemplate();
    }

    private async Task<string> RenderTemplate(string template, object data)
    {
        var json = JsonSerializer.Serialize(data);
        var dataDict = JsonSerializer.Deserialize<Dictionary<string, object>>(json);

        var result = template;
        
        if (dataDict != null)
        {
            foreach (var kvp in dataDict)
            {
                var placeholder = $"{{{{{kvp.Key}}}}}";
                var value = kvp.Value?.ToString() ?? string.Empty;
                result = result.Replace(placeholder, value);
            }
        }

        return await Task.FromResult(result);
    }

    private string GetPasswordResetTemplate()
    {
        return @"
<!DOCTYPE html>
<html lang='pt-BR'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Recuperação de Senha</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #1976d2; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background-color: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
        .button { display: inline-block; background-color: #1976d2; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 20px; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class='header'>
        <h1>{{CompanyName}}</h1>
        <h2>Recuperação de Senha</h2>
    </div>
    <div class='content'>
        <p>Olá <strong>{{UserName}}</strong>,</p>
        <p>Recebemos uma solicitação para redefinir a senha da sua conta no {{CompanyName}}.</p>
        <p>Para criar uma nova senha, clique no botão abaixo:</p>
        <p style='text-align: center;'>
            <a href='{{ResetLink}}' class='button'>Redefinir Senha</a>
        </p>
        <p><strong>Este link expira em 24 horas por motivos de segurança.</strong></p>
        <p>Se você não solicitou esta alteração, pode ignorar este e-mail com segurança. Sua senha atual permanecerá inalterada.</p>
        <p>Se o botão não funcionar, copie e cole o seguinte link no seu navegador:</p>
        <p style='word-break: break-all; background-color: #eee; padding: 10px; border-radius: 4px;'>{{ResetLink}}</p>
    </div>
    <div class='footer'>
        <p>Este é um e-mail automático, por favor não responda.</p>
        <p>&copy; 2024 {{CompanyName}}. Todos os direitos reservados.</p>
    </div>
</body>
</html>";
    }

    private string GetAccountConfirmationTemplate()
    {
        return @"
<!DOCTYPE html>
<html lang='pt-BR'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Confirmação de Conta</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #4caf50; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background-color: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
        .button { display: inline-block; background-color: #4caf50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 20px; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class='header'>
        <h1>{{CompanyName}}</h1>
        <h2>Bem-vindo!</h2>
    </div>
    <div class='content'>
        <p>Olá <strong>{{UserName}}</strong>,</p>
        <p>Obrigado por se cadastrar no {{CompanyName}}!</p>
        <p>Para ativar sua conta e começar a usar nossos serviços, clique no botão abaixo:</p>
        <p style='text-align: center;'>
            <a href='{{ConfirmationLink}}' class='button'>Confirmar Conta</a>
        </p>
        <p>Se o botão não funcionar, copie e cole o seguinte link no seu navegador:</p>
        <p style='word-break: break-all; background-color: #eee; padding: 10px; border-radius: 4px;'>{{ConfirmationLink}}</p>
    </div>
    <div class='footer'>
        <p>Este é um e-mail automático, por favor não responda.</p>
        <p>&copy; 2024 {{CompanyName}}. Todos os direitos reservados.</p>
    </div>
</body>
</html>";
    }
}
