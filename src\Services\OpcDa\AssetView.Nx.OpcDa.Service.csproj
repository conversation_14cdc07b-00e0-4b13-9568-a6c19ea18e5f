<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>dotnet-AssetView.Nx.OpcDa.Service-0c3b6b89-7f0a-4747-9132-b461c072321f</UserSecretsId>
    <UseWindowsForms>true</UseWindowsForms>
    <UseWPF>false</UseWPF>
    <EnableComHosting>true</EnableComHosting>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="9.0.0" />
    <PackageReference Include="System.Runtime.InteropServices" Version="4.3.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
  </ItemGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Modules\OpcDa\AssetView.Nx.Modules.OpcDa.csproj" />
    <ProjectReference Include="..\..\Core\AssetView.Nx.Core.csproj" />
    <ProjectReference Include="..\..\Shared\AssetView.Nx.Shared.csproj" />
  </ItemGroup>

</Project>
