using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Hosting;
using AssetView.Nx.Modules.OpcDa.Interfaces;
using AssetView.Nx.Modules.OpcDa.DTOs;
using AssetView.Nx.Modules.OpcDa.Models;

namespace AssetView.Nx.Modules.OpcDa.Hubs;

/// <summary>
/// Hub SignalR para transmissão de dados OPC em tempo real
/// </summary>
public class OpcDataHub : Hub
{
    private readonly ILogger<OpcDataHub> _logger;
    private readonly IOpcDaService _opcService;

    public OpcDataHub(ILogger<OpcDataHub> logger, IOpcDaService opcService)
    {
        _logger = logger;
        _opcService = opcService;
    }

    /// <summary>
    /// Conecta cliente ao hub
    /// </summary>
    public override async Task OnConnectedAsync()
    {
        _logger.LogInformation("Cliente conectado ao OPC Hub: {ConnectionId}", Context.ConnectionId);
        
        // Enviar status atual da conexão
        var status = _opcService.GetConnectionStatus();
        await Clients.Caller.SendAsync("ConnectionStatusUpdate", new OpcConnectionStatusDto
        {
            Status = status.ToString(),
            Timestamp = DateTime.UtcNow,
            Message = "Cliente conectado ao hub"
        });

        await base.OnConnectedAsync();
    }

    /// <summary>
    /// Desconecta cliente do hub
    /// </summary>
    public override async Task OnDisconnectedAsync(Exception? exception)
    {
        _logger.LogInformation("Cliente desconectado do OPC Hub: {ConnectionId}", Context.ConnectionId);
        
        if (exception != null)
        {
            _logger.LogError(exception, "Cliente desconectado com erro: {ConnectionId}", Context.ConnectionId);
        }

        await base.OnDisconnectedAsync(exception);
    }

    /// <summary>
    /// Inscreve cliente em um grupo específico de tags
    /// </summary>
    /// <param name="groupName">Nome do grupo</param>
    public async Task JoinGroup(string groupName)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
        _logger.LogInformation("Cliente {ConnectionId} inscrito no grupo {GroupName}", Context.ConnectionId, groupName);
        
        await Clients.Caller.SendAsync("GroupJoined", groupName);
    }

    /// <summary>
    /// Remove inscrição do cliente de um grupo
    /// </summary>
    /// <param name="groupName">Nome do grupo</param>
    public async Task LeaveGroup(string groupName)
    {
        await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
        _logger.LogInformation("Cliente {ConnectionId} removido do grupo {GroupName}", Context.ConnectionId, groupName);
        
        await Clients.Caller.SendAsync("GroupLeft", groupName);
    }

    /// <summary>
    /// Solicita leitura de um item específico
    /// </summary>
    /// <param name="tagName">Nome do tag</param>
    public async Task RequestItemRead(string tagName)
    {
        try
        {
            var item = await _opcService.ReadItemAsync(tagName);
            if (item != null)
            {
                var dto = new OpcDataUpdateDto
                {
                    TagName = item.TagName,
                    Value = item.Value,
                    Quality = item.Quality.ToString(),
                    Timestamp = item.Timestamp,
                    DataType = item.DataType?.Name,
                    GroupName = item.GroupName
                };

                await Clients.Caller.SendAsync("ItemDataUpdate", dto);
            }
            else
            {
                await Clients.Caller.SendAsync("ItemReadError", tagName, "Item não encontrado");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao ler item {TagName} para cliente {ConnectionId}", tagName, Context.ConnectionId);
            await Clients.Caller.SendAsync("ItemReadError", tagName, ex.Message);
        }
    }

    /// <summary>
    /// Solicita escrita em um item
    /// </summary>
    /// <param name="tagName">Nome do tag</param>
    /// <param name="value">Valor a ser escrito</param>
    public async Task RequestItemWrite(string tagName, object value)
    {
        try
        {
            var success = await _opcService.WriteItemAsync(tagName, value);
            await Clients.Caller.SendAsync("ItemWriteResult", tagName, success);
            
            if (!success)
            {
                await Clients.Caller.SendAsync("ItemWriteError", tagName, "Falha ao escrever valor");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao escrever item {TagName} para cliente {ConnectionId}", tagName, Context.ConnectionId);
            await Clients.Caller.SendAsync("ItemWriteError", tagName, ex.Message);
        }
    }

    /// <summary>
    /// Solicita estatísticas do servidor OPC
    /// </summary>
    public async Task RequestStatistics()
    {
        try
        {
            var statistics = await _opcService.GetStatisticsAsync();
            await Clients.Caller.SendAsync("StatisticsUpdate", statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter estatísticas para cliente {ConnectionId}", Context.ConnectionId);
            await Clients.Caller.SendAsync("StatisticsError", ex.Message);
        }
    }

    /// <summary>
    /// Solicita lista de todos os itens monitorados
    /// </summary>
    public async Task RequestAllItems()
    {
        try
        {
            var items = await _opcService.GetAllItemsAsync();
            var dtos = items.Select(item => new OpcDataUpdateDto
            {
                TagName = item.TagName,
                Value = item.Value,
                Quality = item.Quality.ToString(),
                Timestamp = item.Timestamp,
                DataType = item.DataType?.Name,
                GroupName = item.GroupName
            }).ToList();

            await Clients.Caller.SendAsync("AllItemsUpdate", dtos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter todos os itens para cliente {ConnectionId}", Context.ConnectionId);
            await Clients.Caller.SendAsync("AllItemsError", ex.Message);
        }
    }
}

/// <summary>
/// Serviço para gerenciar e transmitir atualizações OPC via SignalR
/// </summary>
public class OpcSignalRService : IHostedService
{
    private readonly ILogger<OpcSignalRService> _logger;
    private readonly IOpcDaService _opcService;
    private readonly IHubContext<OpcDataHub> _hubContext;

    public OpcSignalRService(
        ILogger<OpcSignalRService> logger,
        IOpcDaService opcService,
        IHubContext<OpcDataHub> hubContext)
    {
        _logger = logger;
        _opcService = opcService;
        _hubContext = hubContext;
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Iniciando serviço SignalR OPC");

        // Inscrever nos eventos do serviço OPC
        _opcService.DataUpdated += OnDataUpdated;
        _opcService.ConnectionStatusChanged += OnConnectionStatusChanged;

        return Task.CompletedTask;
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Parando serviço SignalR OPC");

        // Desinscrever dos eventos
        _opcService.DataUpdated -= OnDataUpdated;
        _opcService.ConnectionStatusChanged -= OnConnectionStatusChanged;

        return Task.CompletedTask;
    }

    private async void OnDataUpdated(object? sender, OpcDataUpdateEventArgs e)
    {
        try
        {
            var dto = new OpcDataUpdateDto
            {
                TagName = e.Item.TagName,
                Value = e.Item.Value,
                Quality = e.Item.Quality.ToString(),
                Timestamp = e.Item.Timestamp,
                DataType = e.Item.DataType?.Name,
                GroupName = e.Item.GroupName
            };

            // Enviar para todos os clientes
            await _hubContext.Clients.All.SendAsync("DataUpdate", dto);

            // Enviar para grupo específico se definido
            if (!string.IsNullOrEmpty(e.Item.GroupName))
            {
                await _hubContext.Clients.Group(e.Item.GroupName).SendAsync("GroupDataUpdate", dto);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao transmitir atualização de dados via SignalR");
        }
    }

    private async void OnConnectionStatusChanged(object? sender, AssetView.Nx.Modules.OpcDa.Interfaces.OpcConnectionStatusEventArgs e)
    {
        try
        {
            var dto = new OpcConnectionStatusDto
            {
                Status = e.Status.ToString(),
                Timestamp = e.Timestamp,
                Message = e.Message
            };

            await _hubContext.Clients.All.SendAsync("ConnectionStatusUpdate", dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao transmitir status de conexão via SignalR");
        }
    }
}
