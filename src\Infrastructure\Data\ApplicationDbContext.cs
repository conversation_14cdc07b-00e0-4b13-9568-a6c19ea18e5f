using AssetView.Nx.Core.Common;
using AssetView.Nx.Core.Entities;
using AssetView.Nx.Core.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace AssetView.Nx.Infrastructure.Data;

/// <summary>
/// Contexto principal da aplicação
/// </summary>
public class ApplicationDbContext : DbContext
{
    //private readonly ITenantService? _tenantService;
    private readonly ITenantContextFactory? _tenantContextFactory;

    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options, ITenantContextFactory? tenantContextFactory = null)
        : base(options)
    {
        _tenantContextFactory = tenantContextFactory;
    }

    // DbSets
    public DbSet<Tenant> Tenants { get; set; }
    public DbSet<ApplicationUser> Users { get; set; }
    public DbSet<ApplicationRole> Roles { get; set; }
    public DbSet<UserRole> UserRoles { get; set; }
    public DbSet<Project> Projects { get; set; }
    public DbSet<PasswordResetToken> PasswordResetTokens { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configurações das entidades
        ConfigureTenant(modelBuilder);
        ConfigureUser(modelBuilder);
        ConfigureRole(modelBuilder);
        ConfigureUserRole(modelBuilder);
        ConfigureProject(modelBuilder);
        ConfigurePasswordResetToken(modelBuilder);

        // Aplicar filtros globais para multi-tenancy
        ApplyTenantFilters(modelBuilder);

        // Aplicar filtros de soft delete
        ApplySoftDeleteFilters(modelBuilder);
    }

    private void ConfigureTenant(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Tenant>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasMaxLength(50);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
            entity.Property(e => e.Subdomain).IsRequired().HasMaxLength(100);
            entity.HasIndex(e => e.Subdomain).IsUnique();
            entity.Property(e => e.ConnectionString).HasMaxLength(1000);
            entity.Property(e => e.Settings).HasColumnType("TEXT");
        });
    }

    private void ConfigureUser(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<ApplicationUser>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasMaxLength(50);
            entity.Property(e => e.TenantId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.FullName).IsRequired().HasMaxLength(200);
            entity.Property(e => e.Email).IsRequired().HasMaxLength(256);
            entity.Property(e => e.UserName).IsRequired().HasMaxLength(256);
            entity.Property(e => e.PasswordHash).HasMaxLength(500);
            entity.Property(e => e.PhoneNumber).HasMaxLength(50);
            entity.Property(e => e.Avatar).HasMaxLength(1000);
            entity.Property(e => e.Settings).HasColumnType("TEXT");

            entity.HasIndex(e => new { e.TenantId, e.Email }).IsUnique();
            entity.HasIndex(e => new { e.TenantId, e.UserName }).IsUnique();
        });
    }

    private void ConfigureRole(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<ApplicationRole>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasMaxLength(50);
            entity.Property(e => e.TenantId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(256);
            entity.Property(e => e.NormalizedName).HasMaxLength(256);
            entity.Property(e => e.Description).HasMaxLength(500);

            entity.HasIndex(e => new { e.TenantId, e.Name }).IsUnique();
        });
    }

    private void ConfigureUserRole(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<UserRole>(entity =>
        {
            entity.HasKey(e => new { e.UserId, e.RoleId });
            entity.Property(e => e.UserId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.RoleId).IsRequired().HasMaxLength(50);

            entity.HasOne(e => e.User)
                  .WithMany()
                  .HasForeignKey(e => e.UserId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.Role)
                  .WithMany()
                  .HasForeignKey(e => e.RoleId)
                  .OnDelete(DeleteBehavior.Cascade);
        });
    }

    private void ConfigureProject(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Project>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasMaxLength(50);
            entity.Property(e => e.TenantId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
            entity.Property(e => e.Description).HasMaxLength(1000);
            entity.Property(e => e.OwnerId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Budget).HasColumnType("decimal(18,2)");
            entity.Property(e => e.Tags).HasColumnType("TEXT");
            entity.Property(e => e.Settings).HasColumnType("TEXT");

            entity.HasOne(e => e.Owner)
                  .WithMany()
                  .HasForeignKey(e => e.OwnerId)
                  .OnDelete(DeleteBehavior.Restrict);
        });
    }

    private void ConfigurePasswordResetToken(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<PasswordResetToken>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Id).HasMaxLength(50);
            entity.Property(e => e.TenantId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.UserId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Token).IsRequired().HasMaxLength(500);
            entity.Property(e => e.ExpiresAt).IsRequired();
            entity.Property(e => e.IsUsed).IsRequired();
            entity.Property(e => e.UsedAt);
            entity.Property(e => e.RequestIpAddress).HasMaxLength(45); // IPv6 max length
            entity.Property(e => e.RequestUserAgent).HasMaxLength(1000);
            entity.Property(e => e.UsedIpAddress).HasMaxLength(45);
            entity.Property(e => e.UsedUserAgent).HasMaxLength(1000);

            // Relacionamentos
            entity.HasOne(e => e.User)
                  .WithMany()
                  .HasForeignKey(e => e.UserId)
                  .OnDelete(DeleteBehavior.Cascade);

            // Índices
            entity.HasIndex(e => e.Token).IsUnique();
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => e.ExpiresAt);
            entity.HasIndex(e => new { e.UserId, e.IsUsed, e.ExpiresAt });
        });
    }

    private void ApplyTenantFilters(ModelBuilder modelBuilder)
    {
        // Aplicar filtro de tenant para entidades que implementam ITenantEntity
        modelBuilder.Entity<ApplicationUser>().HasQueryFilter(e => e.TenantId == GetCurrentTenantId());
        modelBuilder.Entity<ApplicationRole>().HasQueryFilter(e => e.TenantId == GetCurrentTenantId());
        modelBuilder.Entity<Project>().HasQueryFilter(e => e.TenantId == GetCurrentTenantId());
        modelBuilder.Entity<PasswordResetToken>().HasQueryFilter(e => e.TenantId == GetCurrentTenantId());
    }

    private void ApplySoftDeleteFilters(ModelBuilder modelBuilder)
    {
        // Aplicar filtro de soft delete para entidades que herdam de BaseEntity
        modelBuilder.Entity<Tenant>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<ApplicationUser>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<ApplicationRole>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<Project>().HasQueryFilter(e => !e.IsDeleted);
        modelBuilder.Entity<PasswordResetToken>().HasQueryFilter(e => !e.IsDeleted);
    }

    private string GetCurrentTenantId()
    {
        //return _tenantService?.GetCurrentTenantId() ?? string.Empty;
        return _tenantContextFactory?.GetCurrentTenantId() ?? string.Empty;
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Aplicar auditoria automática
        ApplyAuditInformation();

        return await base.SaveChangesAsync(cancellationToken);
    }

    private void ApplyAuditInformation()
    {
        var currentTenantId = GetCurrentTenantId();
        var currentTime = DateTime.UtcNow;

        foreach (var entry in ChangeTracker.Entries<BaseEntity>())
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedAt = currentTime;
                    entry.Entity.UpdatedAt = currentTime;
                    break;

                case EntityState.Modified:
                    entry.Entity.UpdatedAt = currentTime;
                    break;
            }
        }

        // Aplicar TenantId automaticamente para entidades que implementam ITenantEntity
        foreach (var entry in ChangeTracker.Entries<ITenantEntity>())
        {
            if (entry.State == EntityState.Added && string.IsNullOrEmpty(entry.Entity.TenantId))
            {
                entry.Entity.TenantId = currentTenantId;
            }
        }
    }
}
