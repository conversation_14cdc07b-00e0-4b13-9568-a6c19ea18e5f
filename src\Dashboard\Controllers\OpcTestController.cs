using Microsoft.AspNetCore.Mvc;
using AssetView.Nx.Modules.OpcDa.Interfaces;
using AssetView.Nx.Modules.OpcDa.Services;
using AssetView.Nx.Modules.OpcDa.Models;

namespace AssetView.Nx.Dashboard.Controllers;

[ApiController]
[Route("api/[controller]")]
public class OpcTestController : ControllerBase
{
    private readonly IOpcDiscoveryService _discoveryService;
    private readonly OpcRealBrowsingService _browsingService;
    private readonly ILogger<OpcTestController> _logger;

    public OpcTestController(
        IOpcDiscoveryService discoveryService,
        OpcRealBrowsingService browsingService,
        ILogger<OpcTestController> logger)
    {
        _discoveryService = discoveryService;
        _browsingService = browsingService;
        _logger = logger;
    }

    [HttpPost("test-connect")]
    public async Task<IActionResult> TestConnect()
    {
        try
        {
            _logger.LogInformation("=== TESTE MANUAL: CONECTAR OPC ===");
            
            // Descobrir servidores
            var servers = await _discoveryService.DiscoverServersAsync("localhost");
            _logger.LogInformation("Servidores encontrados: {Count}", servers.Count());
            
            var firstServer = servers.FirstOrDefault();
            if (firstServer == null)
            {
                return BadRequest("Nenhum servidor OPC encontrado");
            }

            _logger.LogInformation("Tentando conectar ao servidor: {ProgId}", firstServer.ProgId);
            
            // Tentar conectar
            var connected = await _discoveryService.ConnectToServerAsync(firstServer);
            
            return Ok(new { 
                Connected = connected, 
                Server = firstServer.ProgId,
                Message = connected ? "Conectado com sucesso" : "Falha na conexão"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro no teste de conexão");
            return StatusCode(500, ex.Message);
        }
    }

    [HttpPost("test-browse")]
    public async Task<IActionResult> TestBrowse()
    {
        try
        {
            _logger.LogInformation("=== TESTE MANUAL: BROWSING TAGS ===");
            
            // Usar um servidor simulado para teste
            var testProgId = "Matrikon.OPC.Simulation.1";
            var testHost = "localhost";
            
            _logger.LogInformation("Testando browsing para: {ProgId}@{Host}", testProgId, testHost);
            
            // Tentar browsing
            var tags = await _browsingService.BrowseRealTagsAsync(testProgId, testHost, null);
            
            _logger.LogInformation("Tags encontrados: {Count}", tags.Count);
            
            return Ok(new { 
                TagCount = tags.Count,
                Tags = tags.Take(10).Select(t => new { t.Name, t.FullPath, t.IsLeaf, t.IsBranch })
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro no teste de browsing");
            return StatusCode(500, ex.Message);
        }
    }
}
