using AssetView.Nx.Core.Entities;
using AssetView.Nx.Shared.Models;

namespace AssetView.Nx.Modules.Users.Services;

/// <summary>
/// Interface para serviços de gestão de usuários
/// </summary>
public interface IUserManagementService
{
    /// <summary>
    /// Obtém todos os usuários do tenant atual
    /// </summary>
    Task<IEnumerable<ApplicationUser>> GetUsersAsync();

    /// <summary>
    /// Obtém usuários paginados
    /// </summary>
    Task<PagedResult<ApplicationUser>> GetUsersPagedAsync(int page, int pageSize, string? searchTerm = null);

    /// <summary>
    /// Obtém usuário por ID
    /// </summary>
    Task<ApplicationUser?> GetUserByIdAsync(string userId);

    /// <summary>
    /// Cria um novo usuário
    /// </summary>
    Task<UserResult> CreateUserAsync(CreateUserRequest request);

    /// <summary>
    /// Atualiza um usuário
    /// </summary>
    Task<UserResult> UpdateUserAsync(string userId, UpdateUserRequest request);

    /// <summary>
    /// Ativa/desativa um usuário
    /// </summary>
    Task<UserResult> ToggleUserStatusAsync(string userId);

    /// <summary>
    /// Exclui um usuário (soft delete)
    /// </summary>
    Task<UserResult> DeleteUserAsync(string userId);

    /// <summary>
    /// Obtém roles do usuário
    /// </summary>
    Task<IEnumerable<ApplicationRole>> GetUserRolesAsync(string userId);

    /// <summary>
    /// Adiciona role ao usuário
    /// </summary>
    Task<UserResult> AddUserToRoleAsync(string userId, string roleId);

    /// <summary>
    /// Remove role do usuário
    /// </summary>
    Task<UserResult> RemoveUserFromRoleAsync(string userId, string roleId);

    /// <summary>
    /// Obtém todos os roles disponíveis
    /// </summary>
    Task<IEnumerable<ApplicationRole>> GetAvailableRolesAsync();
}

/// <summary>
/// Resultado de operações de usuário
/// </summary>
public class UserResult
{
    public bool Success { get; set; }
    public string? Message { get; set; }
    public List<string> Errors { get; set; } = new();
    public ApplicationUser? User { get; set; }
}



/// <summary>
/// Request para criação de usuário
/// </summary>
public class CreateUserRequest
{
    public string FullName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
    public string Password { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public List<string> RoleIds { get; set; } = new();
}

/// <summary>
/// Request para atualização de usuário
/// </summary>
public class UpdateUserRequest
{
    public string FullName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
    public bool IsActive { get; set; }
    public List<string> RoleIds { get; set; } = new();
}
