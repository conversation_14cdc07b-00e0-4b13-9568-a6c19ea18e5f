using AssetView.Nx.Core.Entities;

namespace AssetView.Nx.Modules.Auth.Services;

/// <summary>
/// Interface para serviços de autenticação
/// </summary>
public interface IAuthService
{
    /// <summary>
    /// Registra um novo usuário
    /// </summary>
    Task<AuthResult> RegisterAsync(RegisterRequest request);

    /// <summary>
    /// Autentica um usuário
    /// </summary>
    Task<AuthResult> LoginAsync(LoginRequest request);

    /// <summary>
    /// Faz logout do usuário
    /// </summary>
    Task LogoutAsync(string userId);

    /// <summary>
    /// Confirma o email do usuário
    /// </summary>
    Task<AuthResult> ConfirmEmailAsync(string userId, string token);

    /// <summary>
    /// Solicita reset de senha
    /// </summary>
    Task<AuthResult> ForgotPasswordAsync(string email);

    /// <summary>
    /// Reseta a senha do usuário
    /// </summary>
    Task<AuthResult> ResetPasswordAsync(ResetPasswordRequest request);

    /// <summary>
    /// Valida um token de reset de senha
    /// </summary>
    Task<AuthResult> ValidateResetTokenAsync(string token);

    /// <summary>
    /// Altera a senha do usuário
    /// </summary>
    Task<AuthResult> ChangePasswordAsync(string userId, string currentPassword, string newPassword);

    /// <summary>
    /// Habilita 2FA para o usuário
    /// </summary>
    Task<TwoFactorResult> EnableTwoFactorAsync(string userId);

    /// <summary>
    /// Desabilita 2FA para o usuário
    /// </summary>
    Task<AuthResult> DisableTwoFactorAsync(string userId);

    /// <summary>
    /// Verifica o código 2FA
    /// </summary>
    Task<AuthResult> VerifyTwoFactorAsync(string userId, string code);

    /// <summary>
    /// Obtém informações do usuário atual
    /// </summary>
    Task<ApplicationUser?> GetCurrentUserAsync();
}

/// <summary>
/// Resultado de operações de autenticação
/// </summary>
public class AuthResult
{
    public bool Success { get; set; }
    public string? Message { get; set; }
    public List<string> Errors { get; set; } = new();
    public ApplicationUser? User { get; set; }
    public string? Token { get; set; }
}

/// <summary>
/// Resultado de operações de 2FA
/// </summary>
public class TwoFactorResult : AuthResult
{
    public string? QrCodeUri { get; set; }
    public string? SharedKey { get; set; }
    public string[]? RecoveryCodes { get; set; }
}

/// <summary>
/// Request para registro de usuário
/// </summary>
public class RegisterRequest
{
    public string FullName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
}

/// <summary>
/// Request para login
/// </summary>
public class LoginRequest
{
    public string EmailOrUserName { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public bool RememberMe { get; set; }
    public string? TwoFactorCode { get; set; }
}

/// <summary>
/// Request para reset de senha
/// </summary>
public class ResetPasswordRequest
{
    public string Email { get; set; } = string.Empty;
    public string Token { get; set; } = string.Empty;
    public string NewPassword { get; set; } = string.Empty;
}
