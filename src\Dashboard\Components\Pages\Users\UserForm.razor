@page "/users/create"
@page "/users/edit/{UserId}"
@inherits AdminPageBase
@inject IUserManagementService UserService
@inject ISnackbar Snackbar
@using AssetView.Nx.Modules.Users.Services
@using AssetView.Nx.Core.Entities
@using AssetView.Nx.Dashboard.Components.Shared
@using System.ComponentModel.DataAnnotations

<PageTitle>@(_isEdit ? "Editar Usuário" : "Novo Usuário") - Smar Hart System</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large">
    <div class="d-flex align-center mb-4">
        <MudIconButton Icon="@Icons.Material.Filled.ArrowBack" OnClick="GoBack" />
        <MudText Typo="Typo.h4" Class="ml-2">@(_isEdit ? "Editar Usuário" : "Novo Usuário")</MudText>
    </div>

    <MudCard Elevation="2">
        <MudCardContent>
            <EditForm Model="_model" OnValidSubmit="SaveUser">
                <DataAnnotationsValidator />
                
                <MudGrid>
                    <!-- Informações Básicas -->
                    <MudItem xs="12">
                        <MudText Typo="Typo.h6" Class="mb-4">Informações Básicas</MudText>
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="_model.FullName"
                                      Label="Nome Completo"
                                      Variant="Variant.Outlined"
                                      Required="true"
                                      RequiredError="Nome completo é obrigatório"
                                      For="@(() => _model.FullName)"
                                      Disabled="@_saving" />
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="_model.UserName"
                                      Label="Nome de Usuário"
                                      Variant="Variant.Outlined"
                                      Required="true"
                                      RequiredError="Nome de usuário é obrigatório"
                                      For="@(() => _model.UserName)"
                                      Disabled="@_saving" />
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="_model.Email"
                                      Label="E-mail"
                                      Variant="Variant.Outlined"
                                      InputType="InputType.Email"
                                      Required="true"
                                      RequiredError="E-mail é obrigatório"
                                      For="@(() => _model.Email)"
                                      Disabled="@_saving" />
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="_model.PhoneNumber"
                                      Label="Telefone"
                                      Variant="Variant.Outlined"
                                      InputType="InputType.Telephone"
                                      For="@(() => _model.PhoneNumber)"
                                      Disabled="@_saving" />
                    </MudItem>

                    <!-- Senha (apenas para criação) -->
                    @if (!_isEdit)
                    {
                        <MudItem xs="12">
                            <MudText Typo="Typo.h6" Class="mb-4 mt-4">Credenciais</MudText>
                        </MudItem>
                        
                        <MudItem xs="12" md="6">
                            <MudTextField @bind-Value="_model.Password"
                                          Label="Senha"
                                          Variant="Variant.Outlined"
                                          InputType="@(_showPassword ? InputType.Text : InputType.Password)"
                                          Required="true"
                                          RequiredError="Senha é obrigatória"
                                          For="@(() => _model.Password)"
                                          Disabled="@_saving"
                                          Adornment="Adornment.End"
                                          AdornmentIcon="@(_showPassword ? Icons.Material.Filled.Visibility : Icons.Material.Filled.VisibilityOff)"
                                          OnAdornmentClick="TogglePasswordVisibility" />
                        </MudItem>
                        
                        <MudItem xs="12" md="6">
                            <MudTextField @bind-Value="_model.ConfirmPassword"
                                          Label="Confirmar Senha"
                                          Variant="Variant.Outlined"
                                          InputType="@(_showConfirmPassword ? InputType.Text : InputType.Password)"
                                          Required="true"
                                          RequiredError="Confirmação de senha é obrigatória"
                                          For="@(() => _model.ConfirmPassword)"
                                          Disabled="@_saving"
                                          Adornment="Adornment.End"
                                          AdornmentIcon="@(_showConfirmPassword ? Icons.Material.Filled.Visibility : Icons.Material.Filled.VisibilityOff)"
                                          OnAdornmentClick="ToggleConfirmPasswordVisibility" />
                        </MudItem>
                    }

                    <!-- Status -->
                    <MudItem xs="12">
                        <MudText Typo="Typo.h6" Class="mb-4 mt-4">Status</MudText>
                    </MudItem>
                    
                    <MudItem xs="12" md="6">
                        <MudSwitch @bind-Value="_model.IsActive"
                                   Label="Usuário Ativo"
                                   Color="Color.Primary"
                                   Disabled="@_saving" />
                    </MudItem>

                    <!-- Roles -->
                    <MudItem xs="12">
                        <MudText Typo="Typo.h6" Class="mb-4 mt-4">Permissões</MudText>
                    </MudItem>
                    
                    <MudItem xs="12">
                        @if (_availableRoles.Any())
                        {
                            <MudSelect T="string" 
                                       MultiSelection="true" 
                                       @bind-SelectedValues="_selectedRoles"
                                       Label="Roles"
                                       Variant="Variant.Outlined"
                                       MultiSelectionTextFunc="@(new Func<List<string>, string>(GetMultiSelectionText))"
                                       Disabled="@_saving">
                                @foreach (var role in _availableRoles)
                                {
                                    <MudSelectItem T="string" Value="@role.Id">
                                        <div class="d-flex align-center">
                                            <MudIcon Icon="@Icons.Material.Filled.Security" Size="Size.Small" Class="mr-2" />
                                            <div>
                                                <MudText Typo="Typo.body1">@role.Name</MudText>
                                                @if (!string.IsNullOrEmpty(role.Description))
                                                {
                                                    <MudText Typo="Typo.caption" Color="Color.Secondary">@role.Description</MudText>
                                                }
                                            </div>
                                        </div>
                                    </MudSelectItem>
                                }
                            </MudSelect>
                        }
                        else
                        {
                            <MudAlert Severity="Severity.Info">
                                Nenhuma role disponível. Crie roles primeiro para atribuir aos usuários.
                            </MudAlert>
                        }
                    </MudItem>
                </MudGrid>
            </EditForm>
        </MudCardContent>
        
        <MudCardActions>
            <MudButton Variant="Variant.Text" OnClick="GoBack" Disabled="@_saving">
                Cancelar
            </MudButton>
            <MudButton Variant="Variant.Filled" 
                       Color="Color.Primary" 
                       OnClick="SaveUser"
                       Disabled="@_saving"
                       StartIcon="@Icons.Material.Filled.Save">
                @if (_saving)
                {
                    <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                    <MudText Class="ms-2">Salvando...</MudText>
                }
                else
                {
                    <MudText>@(_isEdit ? "Atualizar" : "Criar") Usuário</MudText>
                }
            </MudButton>
        </MudCardActions>
    </MudCard>
</MudContainer>

@code {
    [Parameter] public string? UserId { get; set; }

    private bool _isEdit => !string.IsNullOrEmpty(UserId);
    private bool _saving = false;
    private bool _showPassword = false;
    private bool _showConfirmPassword = false;
    private UserFormModel _model = new();
    private List<ApplicationRole> _availableRoles = new();
    private IEnumerable<string> _selectedRoles = new HashSet<string>();

    protected override async Task OnInitializedAsync()
    {
        await LoadRoles();
        
        if (_isEdit && !string.IsNullOrEmpty(UserId))
        {
            await LoadUser();
        }
    }

    private async Task LoadRoles()
    {
        try
        {
            _availableRoles = (await UserService.GetAvailableRolesAsync()).ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao carregar roles: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadUser()
    {
        try
        {
            var user = await UserService.GetUserByIdAsync(UserId!);
            if (user == null)
            {
                Snackbar.Add("Usuário não encontrado", Severity.Error);
                Navigation.NavigateTo("/users");
                return;
            }

            _model.FullName = user.FullName;
            _model.UserName = user.UserName;
            _model.Email = user.Email;
            _model.PhoneNumber = user.PhoneNumber;
            _model.IsActive = user.IsActive;

            // Carregar roles do usuário
            var userRoles = await UserService.GetUserRolesAsync(UserId!);
            _selectedRoles = userRoles.Select(r => r.Id).ToHashSet();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao carregar usuário: {ex.Message}", Severity.Error);
            Navigation.NavigateTo("/users");
        }
    }

    private async Task SaveUser()
    {
        if (_saving) return;

        _saving = true;
        try
        {
            UserResult result;

            if (_isEdit)
            {
                var updateRequest = new UpdateUserRequest
                {
                    FullName = _model.FullName,
                    UserName = _model.UserName,
                    Email = _model.Email,
                    PhoneNumber = _model.PhoneNumber,
                    IsActive = _model.IsActive,
                    RoleIds = _selectedRoles.ToList()
                };

                result = await UserService.UpdateUserAsync(UserId!, updateRequest);
            }
            else
            {
                var createRequest = new CreateUserRequest
                {
                    FullName = _model.FullName,
                    UserName = _model.UserName,
                    Email = _model.Email,
                    PhoneNumber = _model.PhoneNumber,
                    Password = _model.Password,
                    IsActive = _model.IsActive,
                    RoleIds = _selectedRoles.ToList()
                };

                result = await UserService.CreateUserAsync(createRequest);
            }

            if (result.Success)
            {
                Snackbar.Add(result.Message ?? "Usuário salvo com sucesso!", Severity.Success);
                Navigation.NavigateTo("/users");
            }
            else
            {
                if (result.Errors.Any())
                {
                    foreach (var error in result.Errors)
                    {
                        Snackbar.Add(error, Severity.Error);
                    }
                }
                else
                {
                    Snackbar.Add(result.Message ?? "Erro ao salvar usuário", Severity.Error);
                }
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao salvar usuário: {ex.Message}", Severity.Error);
        }
        finally
        {
            _saving = false;
        }
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/users");
    }

    private void TogglePasswordVisibility()
    {
        _showPassword = !_showPassword;
    }

    private void ToggleConfirmPasswordVisibility()
    {
        _showConfirmPassword = !_showConfirmPassword;
    }

    private string GetMultiSelectionText(List<string> selectedValues)
    {
        if (!selectedValues.Any())
            return "Selecione as roles";

        if (selectedValues.Count == 1)
        {
            var role = _availableRoles.FirstOrDefault(r => r.Id == selectedValues.First());
            return role?.Name ?? "Role selecionada";
        }

        return $"{selectedValues.Count} roles selecionadas";
    }

    public class UserFormModel
    {
        [Required(ErrorMessage = "Nome completo é obrigatório")]
        [StringLength(200, ErrorMessage = "Nome completo deve ter no máximo 200 caracteres")]
        public string FullName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Nome de usuário é obrigatório")]
        [StringLength(256, ErrorMessage = "Nome de usuário deve ter no máximo 256 caracteres")]
        public string UserName { get; set; } = string.Empty;

        [Required(ErrorMessage = "E-mail é obrigatório")]
        [EmailAddress(ErrorMessage = "E-mail inválido")]
        [StringLength(256, ErrorMessage = "E-mail deve ter no máximo 256 caracteres")]
        public string Email { get; set; } = string.Empty;

        [Phone(ErrorMessage = "Telefone inválido")]
        [StringLength(50, ErrorMessage = "Telefone deve ter no máximo 50 caracteres")]
        public string? PhoneNumber { get; set; }

        [Required(ErrorMessage = "Senha é obrigatória")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "Senha deve ter entre 6 e 100 caracteres")]
        public string Password { get; set; } = string.Empty;

        [Required(ErrorMessage = "Confirmação de senha é obrigatória")]
        [Compare(nameof(Password), ErrorMessage = "Senhas não coincidem")]
        public string ConfirmPassword { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;
    }
}
