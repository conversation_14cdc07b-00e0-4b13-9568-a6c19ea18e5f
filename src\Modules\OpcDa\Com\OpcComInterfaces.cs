using System.Runtime.InteropServices;

namespace AssetView.Nx.Modules.OpcDa.Com;

/// <summary>
/// Interfaces COM para OPC DA
/// Baseadas nas especificações OPC DA 2.0
/// </summary>

#region OPC Server Interfaces

[ComImport]
[Guid("39c13a4d-011e-11d0-9675-0020afd8adb3")]
[InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
public interface IOPCServer
{
    void AddGroup(
        [In, MarshalAs(UnmanagedType.LPWStr)] string szName,
        [In] bool bActive,
        [In] int dwRequestedUpdateRate,
        [In] int hClientGroup,
        [In] IntPtr pTimeBias,
        [In] IntPtr pPercentDeadband,
        [In] int dwLCID,
        [Out] out int phServerGroup,
        [Out] out int pRevisedUpdateRate,
        [In] ref Guid riid,
        [Out, MarshalAs(UnmanagedType.IUnknown)] out object ppUnk);

    void GetErrorString(
        [In] int dwError,
        [In] int dwLocale,
        [Out, MarshalAs(UnmanagedType.LPWStr)] out string ppString);

    void GetGroupByName(
        [In, MarshalAs(UnmanagedType.LPWStr)] string szName,
        [In] ref Guid riid,
        [Out, MarshalAs(UnmanagedType.IUnknown)] out object ppUnk);

    void GetStatus([Out] out IntPtr ppServerStatus);

    void RemoveGroup(
        [In] int hServerGroup,
        [In] bool bForce);

    void CreateGroupEnumerator(
        [In] int dwScope,
        [In] ref Guid riid,
        [Out, MarshalAs(UnmanagedType.IUnknown)] out object ppUnk);
}

[ComImport]
[Guid("39c13a50-011e-11d0-9675-0020afd8adb3")]
[InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
public interface IOPCGroup
{
    void GetState(
        [Out] out int pUpdateRate,
        [Out] out bool pActive,
        [Out, MarshalAs(UnmanagedType.LPWStr)] out string ppName,
        [Out] out int pTimeBias,
        [Out] out float pPercentDeadband,
        [Out] out int pLCID,
        [Out] out int phClientGroup,
        [Out] out int phServerGroup);

    void SetState(
        [In] IntPtr pRequestedUpdateRate,
        [Out] out int pRevisedUpdateRate,
        [In] IntPtr pActive,
        [In] IntPtr pTimeBias,
        [In] IntPtr pPercentDeadband,
        [In] IntPtr pLCID,
        [In] IntPtr phClientGroup);

    void SetName([In, MarshalAs(UnmanagedType.LPWStr)] string szName);

    void CloneGroup(
        [In, MarshalAs(UnmanagedType.LPWStr)] string szName,
        [In] ref Guid riid,
        [Out, MarshalAs(UnmanagedType.IUnknown)] out object ppUnk);

    void AddItems(
        [In] int dwCount,
        [In] IntPtr pItemArray,
        [Out] out IntPtr ppResults,
        [Out] out IntPtr ppErrors);

    void ValidateItems(
        [In] int dwCount,
        [In] IntPtr pItemArray,
        [In] bool bBlobUpdate,
        [Out] out IntPtr ppValidationResults,
        [Out] out IntPtr ppErrors);

    void RemoveItems(
        [In] int dwCount,
        [In] IntPtr phServer,
        [Out] out IntPtr ppErrors);

    void SetActiveState(
        [In] int dwCount,
        [In] IntPtr phServer,
        [In] bool bActive,
        [Out] out IntPtr ppErrors);

    void SetClientHandles(
        [In] int dwCount,
        [In] IntPtr phServer,
        [In] IntPtr phClient,
        [Out] out IntPtr ppErrors);

    void SetDatatypes(
        [In] int dwCount,
        [In] IntPtr phServer,
        [In] IntPtr pRequestedDatatypes,
        [Out] out IntPtr ppErrors);

    void CreateEnumerator(
        [In] ref Guid riid,
        [Out, MarshalAs(UnmanagedType.IUnknown)] out object ppUnk);
}

[ComImport]
[Guid("39c13a54-011e-11d0-9675-0020afd8adb3")]
[InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
public interface IOPCSyncIO
{
    void Read(
        [In] int dwSource,
        [In] int dwCount,
        [In] IntPtr phServer,
        [Out] out IntPtr ppItemValues,
        [Out] out IntPtr ppErrors);

    void Write(
        [In] int dwCount,
        [In] IntPtr phServer,
        [In] IntPtr pItemValues,
        [Out] out IntPtr ppErrors);
}

#endregion

#region OPC Data Structures

[StructLayout(LayoutKind.Sequential)]
public struct OPCITEMDEF
{
    [MarshalAs(UnmanagedType.LPWStr)]
    public string szAccessPath;
    [MarshalAs(UnmanagedType.LPWStr)]
    public string szItemID;
    public bool bActive;
    public int hClient;
    public int dwBlobSize;
    public IntPtr pBlob;
    public short vtRequestedDataType;
    public short wReserved;
}

[StructLayout(LayoutKind.Sequential)]
public struct OPCITEMRESULT
{
    public int hServer;
    public short vtCanonicalDataType;
    public short wReserved;
    public int dwAccessRights;
    public int dwBlobSize;
    public IntPtr pBlob;
}

[StructLayout(LayoutKind.Sequential)]
public struct OPCITEMSTATE
{
    public int hClient;
    public long ftTimeStamp;
    public short wQuality;
    public short wReserved;
    public object vDataValue;
}

#endregion

#region Constants

public static class OpcConstants
{
    public const int OPC_DS_CACHE = 1;
    public const int OPC_DS_DEVICE = 2;

    public const short OPC_QUALITY_GOOD = 0xC0;
    public const short OPC_QUALITY_UNCERTAIN = 0x40;
    public const short OPC_QUALITY_BAD = 0x00;

    public const int CONNECT_E_ADVISELIMIT = unchecked((int)0x80040202);
    public const int CONNECT_E_NOCONNECTION = unchecked((int)0x80040200);
}

#endregion
