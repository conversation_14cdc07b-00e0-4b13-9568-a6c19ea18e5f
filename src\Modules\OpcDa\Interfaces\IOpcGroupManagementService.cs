using AssetView.Nx.Modules.OpcDa.Models;

namespace AssetView.Nx.Modules.OpcDa.Interfaces;

/// <summary>
/// Interface para gerenciamento de grupos OPC
/// </summary>
public interface IOpcGroupManagementService : IDisposable
{
    /// <summary>
    /// Cria um novo grupo OPC
    /// </summary>
    /// <param name="serverProgId">ProgID do servidor OPC</param>
    /// <param name="config">Configuração do grupo</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Informações do grupo criado</returns>
    Task<OpcGroupInfo> CreateGroupAsync(string serverProgId, OpcGroupConfig config, CancellationToken cancellationToken = default);

    /// <summary>
    /// Adiciona tags a um grupo existente
    /// </summary>
    /// <param name="serverProgId">ProgID do servidor OPC</param>
    /// <param name="groupName">Nome do grupo</param>
    /// <param name="tagPaths">Caminhos dos tags a adicionar</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de subscrições criadas</returns>
    Task<List<OpcTagSubscription>> AddTagsToGroupAsync(string serverProgId, string groupName, List<string> tagPaths, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove tags de um grupo
    /// </summary>
    /// <param name="serverProgId">ProgID do servidor OPC</param>
    /// <param name="groupName">Nome do grupo</param>
    /// <param name="tagPaths">Caminhos dos tags a remover</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    Task RemoveTagsFromGroupAsync(string serverProgId, string groupName, List<string> tagPaths, CancellationToken cancellationToken = default);

    /// <summary>
    /// Lista todos os grupos de um servidor
    /// </summary>
    /// <param name="serverProgId">ProgID do servidor OPC</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Lista de grupos</returns>
    Task<List<OpcGroupInfo>> GetGroupsAsync(string serverProgId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém informações de um grupo específico
    /// </summary>
    /// <param name="serverProgId">ProgID do servidor OPC</param>
    /// <param name="groupName">Nome do grupo</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Informações do grupo ou null se não encontrado</returns>
    Task<OpcGroupInfo?> GetGroupAsync(string serverProgId, string groupName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Ativa ou desativa um grupo
    /// </summary>
    /// <param name="serverProgId">ProgID do servidor OPC</param>
    /// <param name="groupName">Nome do grupo</param>
    /// <param name="active">True para ativar, false para desativar</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    Task SetGroupActiveAsync(string serverProgId, string groupName, bool active, CancellationToken cancellationToken = default);

    /// <summary>
    /// Remove um grupo
    /// </summary>
    /// <param name="serverProgId">ProgID do servidor OPC</param>
    /// <param name="groupName">Nome do grupo</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    Task DeleteGroupAsync(string serverProgId, string groupName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Obtém estatísticas de um grupo
    /// </summary>
    /// <param name="serverProgId">ProgID do servidor OPC</param>
    /// <param name="groupName">Nome do grupo</param>
    /// <param name="cancellationToken">Token de cancelamento</param>
    /// <returns>Estatísticas do grupo ou null se não encontrado</returns>
    Task<OpcGroupStatistics?> GetGroupStatisticsAsync(string serverProgId, string groupName, CancellationToken cancellationToken = default);
}
