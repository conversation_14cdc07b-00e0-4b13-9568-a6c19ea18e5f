# Sistema de Logs - Smar AssetView Nx

Este documento descreve o sistema de logging implementado no Smar AssetView Nx para facilitar o debug e monitoramento das aplicações.

## 📁 **Estrutura de Logs**

```
Smar.AssetView.Nx/
├── logs/                                    # Logs gerais do sistema
├── src/Services/OpcDa/logs/                # Logs do serviço OPC DA
│   ├── opcda-service-YYYYMMDD.log         # Logs de produção
│   └── opcda-service-dev-YYYYMMDD.log     # Logs de desenvolvimento
└── src/AssetView.Nx.Dashboard/logs/          # Logs da aplicação Blazor
    ├── blazor-dashboard-YYYYMMDD.log      # Logs de produção
    └── blazor-dashboard-dev-YYYYMMDD.log  # Logs de desenvolvimento
```

## ⚙️ **Configuração**

### **Serviço OPC DA**

**Produção** (`appsettings.json`):
- <PERSON>ível mínimo: `Information`
- Arquivo: `logs/opcda-service-YYYYMMDD.log`
- Tamanho máximo: 10MB
- Retenção: 10 arquivos

**Desenvolvimento** (`appsettings.Development.json`):
- Nível mínimo: `Debug`
- Arquivo: `logs/opcda-service-dev-YYYYMMDD.log`
- Tamanho máximo: 50MB
- Retenção: 7 arquivos

### **Aplicação Blazor Dashboard**

**Produção** (`appsettings.json`):
- Nível mínimo: `Information`
- Arquivo: `logs/blazor-dashboard-YYYYMMDD.log`
- Tamanho máximo: 10MB
- Retenção: 10 arquivos

**Desenvolvimento** (`appsettings.Development.json`):
- Nível mínimo: `Debug`
- Arquivo: `logs/blazor-dashboard-dev-YYYYMMDD.log`
- Tamanho máximo: 50MB
- Retenção: 7 arquivos

## 📊 **Níveis de Log**

| Nível | Descrição | Quando Usar |
|-------|-----------|-------------|
| `Debug` | Informações detalhadas para debug | Desenvolvimento, troubleshooting |
| `Information` | Informações gerais do fluxo | Eventos importantes, inicialização |
| `Warning` | Situações que podem causar problemas | Reconexões, timeouts |
| `Error` | Erros que não param a aplicação | Falhas de conexão, erros de dados |
| `Fatal` | Erros críticos que param a aplicação | Falhas de inicialização |

## 🔍 **Categorias de Log Importantes**

### **Serviço OPC DA**
- `AssetView.Nx.Modules.OpcDa.*` - Logs do módulo OPC DA
- `AssetView.Nx.OpcDa.Service.Worker` - Logs do worker principal
- `Microsoft.AspNetCore.SignalR` - Logs do SignalR Hub

### **Aplicação Blazor**
- `AssetView.Nx.Dashboard.Services.OpcSignalRClient` - Cliente SignalR
- `Microsoft.AspNetCore.SignalR.Client` - Cliente SignalR interno
- `AssetView.Nx.Dashboard.Components.Pages.OPC.*` - Páginas OPC

## 📝 **Formato dos Logs**

```
2025-07-01 11:10:30.123 [INF] [AssetView.Nx.OpcDa.Service.Worker] Iniciando serviço OPC DA Worker
2025-07-01 11:10:30.456 [DBG] [AssetView.Nx.Modules.OpcDa.Services.OpcDaService] Conectando ao servidor OPC: Matrikon.OPC.Simulation.1
2025-07-01 11:10:30.789 [WRN] [AssetView.Nx.Dashboard.Services.OpcSignalRClient] Reconectando ao hub OPC SignalR...
```

**Formato**: `{Timestamp} [{Level}] [{SourceContext}] {Message}`

## 🚀 **Como Usar para Debug**

### **1. Monitoramento em Tempo Real**

**PowerShell (Windows):**
```powershell
# Monitorar logs do serviço OPC DA
Get-Content "src\Services\OpcDa\logs\opcda-service-dev-$(Get-Date -Format 'yyyyMMdd').log" -Wait -Tail 50

# Monitorar logs da aplicação Blazor
Get-Content "src\AssetView.Nx.Dashboard\logs\blazor-dashboard-dev-$(Get-Date -Format 'yyyyMMdd').log" -Wait -Tail 50
```

### **2. Filtrar Logs por Categoria**

```powershell
# Filtrar apenas logs de SignalR
Select-String -Path "logs\*.log" -Pattern "SignalR"

# Filtrar apenas erros
Select-String -Path "logs\*.log" -Pattern "\[ERR\]"

# Filtrar logs de conexão OPC
Select-String -Path "logs\*.log" -Pattern "OPC.*[Cc]onect"
```

### **3. Análise de Problemas Comuns**

**Problemas de Conexão OPC:**
```powershell
Select-String -Path "src\Services\OpcDa\logs\*.log" -Pattern "(Erro|Error|Falha|Failed).*OPC"
```

**Problemas de SignalR:**
```powershell
Select-String -Path "logs\*.log" -Pattern "(SignalR|Hub).*(Error|Erro|Failed|Falha)"
```

**Reconexões:**
```powershell
Select-String -Path "logs\*.log" -Pattern "(Reconect|Reconnect)"
```

## 🛠️ **Configuração Personalizada**

### **Adicionar Nova Categoria de Log**

No `appsettings.json`:
```json
{
  "Serilog": {
    "MinimumLevel": {
      "Override": {
        "MinhaCategoria.Personalizada": "Debug"
      }
    }
  }
}
```

### **Adicionar Novo Sink (Destino)**

```json
{
  "Serilog": {
    "WriteTo": [
      {
        "Name": "File",
        "Args": {
          "path": "logs/custom-.log",
          "restrictedToMinimumLevel": "Warning"
        }
      }
    ]
  }
}
```

## 📋 **Checklist de Debug**

### **Problemas de Conexão OPC:**
1. ✅ Verificar logs de inicialização do serviço
2. ✅ Procurar por erros de COM/ProgId
3. ✅ Verificar tentativas de reconexão
4. ✅ Analisar configuração de grupos e itens

### **Problemas de SignalR:**
1. ✅ Verificar logs de conexão do cliente
2. ✅ Procurar por erros de hub
3. ✅ Analisar eventos de reconexão
4. ✅ Verificar URLs e configurações

### **Problemas de Performance:**
1. ✅ Analisar estatísticas nos logs
2. ✅ Verificar taxa de atualizações
3. ✅ Procurar por timeouts
4. ✅ Analisar uso de memória/recursos

## 🔧 **Ferramentas Recomendadas**

- **Visual Studio Code**: Com extensão "Log File Highlighter"
- **PowerShell**: Para análise de logs em linha de comando
- **Notepad++**: Com plugin "Log Viewer"
- **Tail for Windows**: Para monitoramento em tempo real

## 📞 **Suporte**

Para problemas relacionados ao sistema de logs:
1. Verificar se as pastas de logs existem
2. Verificar permissões de escrita
3. Analisar logs de erro da aplicação
4. Verificar configuração do Serilog
