# Implementação com OpcNetApi.Com para Descoberta de Servidores OPC

Este documento explica como usar o `OpcNetApi.Com` que você já tem instalado para descoberta real de servidores OPC DA.

## 🎯 **Diferença: OpcNetApi.Com vs OPC Core Components**

### **OPC Core Components (Sistema Windows):**
```
┌─────────────────────────────────────────┐
│ Windows Registry + COM Components       │
│ ├── IOPCServerList2 (Interface COM)     │
│ ├── IOPCServer (Interface COM)          │
│ ├── OPC Proxy/Stub DLLs                │
│ └── DCOM Configuration                  │
└─────────────────────────────────────────┘
```
- ❌ **Requer instalação separada** (OPC Expert, Matrikon Setup, etc.)
- ❌ **Configuração DCOM complexa**
- ❌ **Dependente de registro do Windows**

### **OpcNetApi.Com (Biblioteca .NET):**
```
┌─────────────────────────────────────────┐
│ OpcNetApi.Com (NuGet Package)          │
│ ├── ServerEnumerator                    │
│ ├── Da.Server                          │
│ ├── Factory                            │
│ └── Specification                      │
└─────────────────────────────────────────┘
```
- ✅ **Já instalado no projeto** via NuGet
- ✅ **Wrapper .NET simplificado**
- ✅ **<PERSON><PERSON> requer OPC Core Components**
- ✅ **Funciona out-of-the-box**

## 🚀 **Implementação com OpcNetApi.Com**

### **1. Descoberta de Servidores**

#### **Código Implementado:**
```csharp
private async Task<List<OpcServerInfo>> DiscoverRegisteredOpcServersAsync(string host)
{
    await Task.Run(() =>
    {
        // Usar OpcNetApi.Com para descoberta de servidores
        var discovery = new ServerEnumerator();
        
        // Descobrir servidores OPC DA 2.0 no host especificado
        var opcServers = discovery.GetAvailableServers(Specification.COM_DA_20, host, null);
        
        foreach (var opcServer in opcServers)
        {
            var serverInfo = CreateServerInfoFromOpcNetApi(opcServer, host);
            servers.Add(serverInfo);
        }
        
        // Fallback para OPC DA 1.0 se não encontrou servidores 2.0
        if (servers.Count == 0)
        {
            var opcServers10 = discovery.GetAvailableServers(Specification.COM_DA_10, host, null);
            // Processar servidores 1.0...
        }
    });
}
```

#### **Especificações Suportadas:**
- **`Specification.COM_DA_10`**: OPC DA 1.0
- **`Specification.COM_DA_20`**: OPC DA 2.0
- **`Specification.COM_DA_30`**: OPC DA 3.0

### **2. Extração de Metadados**

#### **Propriedades do Opc.Server:**
```csharp
private OpcServerInfo? CreateServerInfoFromOpcNetApi(Opc.Server opcServer, string host)
{
    var displayName = opcServer.Name;           // Nome do servidor
    var progId = opcServer.Name;               // Identificador
    var url = opcServer.Url;                   // URL de conexão
    
    // Extrair ProgID da URL se disponível
    if (opcServer.Url != null)
    {
        // URL format: opcda://host/progid
        var urlParts = opcServer.Url.ToString().Split('/');
        if (urlParts.Length > 2)
        {
            progId = urlParts[^1]; // Último segmento
        }
    }
    
    return new OpcServerInfo
    {
        ProgId = progId,
        DisplayName = displayName,
        Host = host,
        IsAvailable = opcServer.Url != null
    };
}
```

### **3. Verificação de Disponibilidade**

#### **Método Simplificado:**
```csharp
private static bool CheckServerAvailabilityOpcNetApi(Opc.Server opcServer)
{
    // Para OpcNetApi, se foi descoberto, assume-se que está disponível
    return opcServer.Url != null;
}
```

#### **Método Avançado (Opcional):**
```csharp
private bool CheckServerAvailabilityAdvanced(Opc.Server opcServer)
{
    try
    {
        using var testServer = new OpcCom.Da.Server(new OpcCom.Factory(), opcServer.Url);
        testServer.Connect();
        testServer.Disconnect();
        return true;
    }
    catch
    {
        return false;
    }
}
```

## 📊 **Vantagens da Implementação OpcNetApi.Com**

### **1. Simplicidade**
- ✅ **Sem instalação adicional** - já está no projeto
- ✅ **API .NET nativa** - sem interop COM complexo
- ✅ **Documentação clara** - biblioteca bem documentada

### **2. Compatibilidade**
- ✅ **Multiplataforma** - funciona em diferentes versões Windows
- ✅ **Sem dependências externas** - não requer OPC Core Components
- ✅ **Fallback automático** - tenta DA 2.0, depois DA 1.0

### **3. Robustez**
- ✅ **Tratamento de erro integrado** - biblioteca trata erros COM
- ✅ **Timeout configurável** - evita travamentos
- ✅ **Liberação automática de recursos** - garbage collection

## 🔧 **Configuração e Uso**

### **1. Descoberta Local:**
```csharp
var discovery = new ServerEnumerator();
var servers = discovery.GetAvailableServers(Specification.COM_DA_20, "localhost", null);
```

### **2. Descoberta Remota:**
```csharp
var discovery = new ServerEnumerator();
var servers = discovery.GetAvailableServers(Specification.COM_DA_20, "*************", null);
```

### **3. Descoberta com Timeout:**
```csharp
var discovery = new ServerEnumerator();
var connectData = new ConnectData(new System.Net.NetworkCredential());
var servers = discovery.GetAvailableServers(Specification.COM_DA_20, host, connectData);
```

## 📋 **Logs de Debug**

### **Logs Típicos de Sucesso:**
```
[DBG] Descobrindo servidores OPC usando OpcNetApi.Com para host: localhost
[DBG] Encontrados 3 servidores OPC via OpcNetApi.Com
[INF] Encontrados 3 servidores OPC no host 'localhost' (3 disponíveis)
```

### **Logs de Fallback:**
```
[WRN] Erro COM ao descobrir servidores OPC: Class not registered (Código: 0x80040154)
[WRN] Erro ao usar OpcNetApi.Com, tentando fallback via registro
[DBG] Usando fallback de descoberta via registro para host: localhost
```

## 🎯 **Comparação: Métodos de Descoberta**

| Método | OpcNetApi.Com | IOPCServerList2 | Registro Windows |
|--------|---------------|-----------------|------------------|
| **Instalação** | ✅ Já instalado | ❌ Requer OPC Core | ✅ Nativo Windows |
| **Complexidade** | ✅ Simples | ❌ Complexo | ⚠️ Médio |
| **Descoberta Remota** | ✅ Nativa | ✅ Via DCOM | ❌ Limitada |
| **Tratamento Erro** | ✅ Integrado | ⚠️ Manual | ⚠️ Manual |
| **Performance** | ✅ Otimizada | ✅ Rápida | ❌ Lenta |
| **Compatibilidade** | ✅ Universal | ⚠️ Dependente | ✅ Windows |

## 🚀 **Como Testar**

### **1. Executar Aplicação:**
```bash
cd src/AssetView.Nx.Dashboard
dotnet run
```

### **2. Acessar Interface:**
- **URL**: `http://localhost:5231/opc/servers`
- **Descoberta**: Clicar "Descobrir" com "localhost"
- **Resultado**: Ver servidores OPC encontrados via OpcNetApi.Com

### **3. Logs Esperados:**
```
[DBG] Descobrindo servidores OPC usando OpcNetApi.Com para host: localhost
[DBG] Encontrados X servidores OPC via OpcNetApi.Com
```

## ⚠️ **Limitações e Considerações**

### **1. Servidores Disponíveis**
- OpcNetApi.Com encontra apenas servidores **realmente instalados**
- Se não há servidores OPC instalados, retorna lista vazia
- Para testes, instalar Matrikon OPC Simulation Server

### **2. Descoberta Remota**
- Requer que o host remoto tenha servidores OPC instalados
- Pode ser bloqueada por firewall/antivírus
- Timeout configurável para evitar travamentos

### **3. Fallback**
- Se OpcNetApi.Com falhar, usa descoberta via registro
- Garante que sempre encontre algum resultado
- Logs indicam qual método foi usado

## 🔄 **Próximos Passos**

1. **Testar com Matrikon**: Instalar Matrikon OPC Simulation Server
2. **Configurar Rede**: Testar descoberta em hosts remotos
3. **Otimizar Cache**: Implementar cache mais inteligente
4. **Métricas**: Adicionar métricas de performance
5. **Timeout**: Configurar timeouts adequados

A implementação com `OpcNetApi.Com` oferece a melhor combinação de **simplicidade**, **robustez** e **compatibilidade** para descoberta de servidores OPC DA!
