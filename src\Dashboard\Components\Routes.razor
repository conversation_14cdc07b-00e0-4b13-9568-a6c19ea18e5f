﻿@using Microsoft.AspNetCore.Components.Authorization

<CascadingAuthenticationState>
    <Router AppAssembly="typeof(Program).Assembly">
        <Found Context="routeData">
            <AuthorizeRouteView RouteData="routeData" DefaultLayout="typeof(Layout.MainLayout)">
                <NotAuthorized>
                    @if (context.User.Identity?.IsAuthenticated != true)
                    {
                        <RedirectToLogin />
                    }
                    else
                    {
                        <div class="d-flex justify-center align-center" style="min-height: 100vh;">
                            <MudCard Elevation="8" Class="pa-8 text-center">
                                <MudCardContent>
                                    <MudIcon Icon="@Icons.Material.Filled.Block"
                                             Color="Color.Error"
                                             Size="Size.Large"
                                             Class="mb-4"
                                             Style="font-size: 4rem;" />

                                    <MudText Typo="Typo.h4" Color="Color.Error" Class="mb-4">
                                        Acesso Negado
                                    </MudText>

                                    <MudText Typo="Typo.body1" Class="mb-6">
                                        Você não tem permissão para acessar esta página.
                                    </MudText>

                                    <MudButton Variant="Variant.Filled"
                                               Color="Color.Primary"
                                               StartIcon="@Icons.Material.Filled.Home"
                                               Href="/">
                                        Página Inicial
                                    </MudButton>
                                </MudCardContent>
                            </MudCard>
                        </div>
                    }
                </NotAuthorized>
            </AuthorizeRouteView>
            <FocusOnNavigate RouteData="routeData" Selector="h1" />
        </Found>
    </Router>
</CascadingAuthenticationState>
