namespace AssetView.Nx.Modules.OpcDa.Models;

/// <summary>
/// Métricas de performance para operações OPC
/// </summary>
public class OpcOperationMetrics
{
    /// <summary>
    /// Tipo da operação (Connect, Read, Write, <PERSON><PERSON><PERSON>, etc.)
    /// </summary>
    public string OperationType { get; set; } = string.Empty;

    /// <summary>
    /// ProgID do servidor OPC
    /// </summary>
    public string? ServerProgId { get; set; }

    /// <summary>
    /// Número total de operações
    /// </summary>
    public long TotalOperations { get; set; }

    /// <summary>
    /// Número de operações bem-sucedidas
    /// </summary>
    public long SuccessfulOperations { get; set; }

    /// <summary>
    /// Número de operações com falha
    /// </summary>
    public long FailedOperations { get; set; }

    /// <summary>
    /// Duração total de todas as operações
    /// </summary>
    public TimeSpan TotalDuration { get; set; }

    /// <summary>
    /// Duração média das operações
    /// </summary>
    public TimeSpan AverageDuration { get; set; }

    /// <summary>
    /// Duração mínima registrada
    /// </summary>
    public TimeSpan MinDuration { get; set; }

    /// <summary>
    /// Duração máxima registrada
    /// </summary>
    public TimeSpan MaxDuration { get; set; }

    /// <summary>
    /// Taxa de sucesso (%)
    /// </summary>
    public double SuccessRate { get; set; }

    /// <summary>
    /// Timestamp da última operação
    /// </summary>
    public DateTime LastOperationTime { get; set; }

    /// <summary>
    /// Erros recentes
    /// </summary>
    public List<OpcErrorRecord> RecentErrors { get; set; } = new();

    /// <summary>
    /// Operações por minuto (média)
    /// </summary>
    public double OperationsPerMinute => TotalOperations > 0 && LastOperationTime > DateTime.MinValue ? 
        TotalOperations / Math.Max(1, (DateTime.UtcNow - LastOperationTime).TotalMinutes) : 0;
}

/// <summary>
/// Métricas de performance para conexões OPC
/// </summary>
public class OpcConnectionMetrics
{
    /// <summary>
    /// ProgID do servidor OPC
    /// </summary>
    public string ServerProgId { get; set; } = string.Empty;

    /// <summary>
    /// Host do servidor
    /// </summary>
    public string Host { get; set; } = string.Empty;

    /// <summary>
    /// Número total de tentativas de conexão
    /// </summary>
    public long TotalConnectionAttempts { get; set; }

    /// <summary>
    /// Número de conexões bem-sucedidas
    /// </summary>
    public long SuccessfulConnections { get; set; }

    /// <summary>
    /// Número de conexões com falha
    /// </summary>
    public long FailedConnections { get; set; }

    /// <summary>
    /// Tempo total gasto conectando
    /// </summary>
    public TimeSpan TotalConnectionTime { get; set; }

    /// <summary>
    /// Tempo médio de conexão
    /// </summary>
    public TimeSpan AverageConnectionTime { get; set; }

    /// <summary>
    /// Tempo mínimo de conexão
    /// </summary>
    public TimeSpan MinConnectionTime { get; set; }

    /// <summary>
    /// Tempo máximo de conexão
    /// </summary>
    public TimeSpan MaxConnectionTime { get; set; }

    /// <summary>
    /// Taxa de sucesso de conexão (%)
    /// </summary>
    public double ConnectionSuccessRate { get; set; }

    /// <summary>
    /// Indica se está atualmente conectado
    /// </summary>
    public bool IsCurrentlyConnected { get; set; }

    /// <summary>
    /// Timestamp da última tentativa de conexão
    /// </summary>
    public DateTime LastAttempt { get; set; }

    /// <summary>
    /// Timestamp da última conexão bem-sucedida
    /// </summary>
    public DateTime LastSuccessfulConnection { get; set; }

    /// <summary>
    /// Timestamp da última falha de conexão
    /// </summary>
    public DateTime LastFailedConnection { get; set; }

    /// <summary>
    /// Timestamp da última desconexão
    /// </summary>
    public DateTime LastDisconnection { get; set; }

    /// <summary>
    /// Número de desconexões
    /// </summary>
    public long DisconnectionCount { get; set; }

    /// <summary>
    /// Tempo total conectado
    /// </summary>
    public TimeSpan TotalConnectedTime { get; set; }

    /// <summary>
    /// Duração média das sessões
    /// </summary>
    public TimeSpan AverageSessionDuration { get; set; }

    /// <summary>
    /// Erros recentes de conexão
    /// </summary>
    public List<OpcErrorRecord> RecentConnectionErrors { get; set; } = new();

    /// <summary>
    /// Tempo desde a última conexão bem-sucedida
    /// </summary>
    public TimeSpan TimeSinceLastConnection => LastSuccessfulConnection > DateTime.MinValue ? 
        DateTime.UtcNow - LastSuccessfulConnection : TimeSpan.MaxValue;

    /// <summary>
    /// Disponibilidade (% do tempo conectado)
    /// </summary>
    public double Availability => TotalConnectionAttempts > 0 ? 
        TotalConnectedTime.TotalMilliseconds / (DateTime.UtcNow - LastAttempt).TotalMilliseconds * 100 : 0;
}

/// <summary>
/// Registro de erro para métricas
/// </summary>
public class OpcErrorRecord
{
    /// <summary>
    /// Timestamp do erro
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Mensagem de erro
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// Código de erro (se disponível)
    /// </summary>
    public int? ErrorCode { get; set; }

    /// <summary>
    /// Contexto adicional
    /// </summary>
    public string? Context { get; set; }
}

/// <summary>
/// Evento de performance OPC
/// </summary>
public class OpcPerformanceEvent
{
    /// <summary>
    /// Timestamp do evento
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Tipo da operação
    /// </summary>
    public string OperationType { get; set; } = string.Empty;

    /// <summary>
    /// ProgID do servidor
    /// </summary>
    public string? ServerProgId { get; set; }

    /// <summary>
    /// Duração da operação
    /// </summary>
    public TimeSpan Duration { get; set; }

    /// <summary>
    /// Indica se a operação foi bem-sucedida
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Mensagem de erro (se houver)
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Contexto adicional
    /// </summary>
    public string? Context { get; set; }

    /// <summary>
    /// Dados adicionais do evento
    /// </summary>
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}

/// <summary>
/// Resumo geral de performance OPC
/// </summary>
public class OpcPerformanceSummary
{
    /// <summary>
    /// Tempo de atividade do serviço
    /// </summary>
    public TimeSpan ServiceUptime { get; set; }

    /// <summary>
    /// Número total de operações
    /// </summary>
    public long TotalOperations { get; set; }

    /// <summary>
    /// Número total de operações bem-sucedidas
    /// </summary>
    public long TotalSuccessfulOperations { get; set; }

    /// <summary>
    /// Número total de operações com falha
    /// </summary>
    public long TotalFailedOperations { get; set; }

    /// <summary>
    /// Taxa geral de sucesso (%)
    /// </summary>
    public double OverallSuccessRate { get; set; }

    /// <summary>
    /// Duração média das operações
    /// </summary>
    public TimeSpan AverageOperationDuration { get; set; }

    /// <summary>
    /// Número total de conexões tentadas
    /// </summary>
    public long TotalConnections { get; set; }

    /// <summary>
    /// Número de conexões ativas
    /// </summary>
    public long ActiveConnections { get; set; }

    /// <summary>
    /// Tempo médio de conexão
    /// </summary>
    public TimeSpan AverageConnectionTime { get; set; }

    /// <summary>
    /// Operações por minuto
    /// </summary>
    public double OperationsPerMinute => ServiceUptime.TotalMinutes > 0 ? 
        TotalOperations / ServiceUptime.TotalMinutes : 0;

    /// <summary>
    /// Timestamp da geração do resumo
    /// </summary>
    public DateTime GeneratedAt { get; set; }

    /// <summary>
    /// Métricas por tipo de operação
    /// </summary>
    public Dictionary<string, OpcOperationMetrics> OperationBreakdown { get; set; } = new();

    /// <summary>
    /// Métricas por servidor
    /// </summary>
    public Dictionary<string, OpcConnectionMetrics> ServerBreakdown { get; set; } = new();
}

/// <summary>
/// Configuração para coleta de métricas
/// </summary>
public class OpcMetricsConfig
{
    /// <summary>
    /// Habilitar coleta de métricas
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Intervalo de processamento de métricas
    /// </summary>
    public TimeSpan ProcessingInterval { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// Tempo máximo para manter eventos
    /// </summary>
    public TimeSpan MaxEventAge { get; set; } = TimeSpan.FromHours(1);

    /// <summary>
    /// Número máximo de eventos a manter
    /// </summary>
    public int MaxEvents { get; set; } = 1000;

    /// <summary>
    /// Número máximo de erros recentes por métrica
    /// </summary>
    public int MaxRecentErrors { get; set; } = 10;

    /// <summary>
    /// Habilitar logging detalhado de métricas
    /// </summary>
    public bool EnableDetailedLogging { get; set; } = false;

    /// <summary>
    /// Habilitar exportação de métricas
    /// </summary>
    public bool EnableMetricsExport { get; set; } = false;

    /// <summary>
    /// Formato de exportação (JSON, CSV, etc.)
    /// </summary>
    public string ExportFormat { get; set; } = "JSON";

    /// <summary>
    /// Caminho para exportação de métricas
    /// </summary>
    public string? ExportPath { get; set; }

    /// <summary>
    /// Intervalo de exportação
    /// </summary>
    public TimeSpan ExportInterval { get; set; } = TimeSpan.FromMinutes(5);
}

/// <summary>
/// Alertas baseados em métricas
/// </summary>
public class OpcMetricsAlert
{
    /// <summary>
    /// ID do alerta
    /// </summary>
    public string AlertId { get; set; } = string.Empty;

    /// <summary>
    /// Nome do alerta
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Descrição do alerta
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Severidade do alerta
    /// </summary>
    public OpcAlertSeverity Severity { get; set; }

    /// <summary>
    /// Condição que dispara o alerta
    /// </summary>
    public string Condition { get; set; } = string.Empty;

    /// <summary>
    /// Valor limite
    /// </summary>
    public double Threshold { get; set; }

    /// <summary>
    /// Timestamp do alerta
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Indica se o alerta está ativo
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Valor atual que disparou o alerta
    /// </summary>
    public double CurrentValue { get; set; }

    /// <summary>
    /// Contexto adicional
    /// </summary>
    public Dictionary<string, object> Context { get; set; } = new();
}

/// <summary>
/// Severidade de alertas
/// </summary>
public enum OpcAlertSeverity
{
    /// <summary>
    /// Informativo
    /// </summary>
    Info,

    /// <summary>
    /// Aviso
    /// </summary>
    Warning,

    /// <summary>
    /// Erro
    /// </summary>
    Error,

    /// <summary>
    /// Crítico
    /// </summary>
    Critical
}

/// <summary>
/// Configuração do pool de conexões OPC
/// </summary>
public class OpcConnectionPoolConfig
{
    /// <summary>
    /// Tamanho máximo do pool por servidor
    /// </summary>
    public int MaxPoolSize { get; set; } = 10;

    /// <summary>
    /// Tempo máximo de inatividade antes de remover conexão
    /// </summary>
    public TimeSpan MaxIdleTime { get; set; } = TimeSpan.FromMinutes(5);

    /// <summary>
    /// Timeout para conexões
    /// </summary>
    public TimeSpan ConnectionTimeout { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// Intervalo de manutenção do pool
    /// </summary>
    public TimeSpan MaintenanceInterval { get; set; } = TimeSpan.FromMinutes(1);

    /// <summary>
    /// Habilitar pool de conexões
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Número mínimo de conexões a manter no pool
    /// </summary>
    public int MinPoolSize { get; set; } = 1;

    /// <summary>
    /// Tempo de vida máximo de uma conexão
    /// </summary>
    public TimeSpan MaxConnectionLifetime { get; set; } = TimeSpan.FromHours(1);
}

/// <summary>
/// Estatísticas do pool de conexões
/// </summary>
public class OpcConnectionPoolStatistics
{
    /// <summary>
    /// Número total de pools
    /// </summary>
    public int TotalPools { get; set; }

    /// <summary>
    /// Número total de conexões
    /// </summary>
    public int TotalConnections { get; set; }

    /// <summary>
    /// Número de conexões ativas
    /// </summary>
    public int ActiveConnections { get; set; }

    /// <summary>
    /// Número de conexões idle
    /// </summary>
    public int IdleConnections { get; set; }

    /// <summary>
    /// Total de conexões criadas
    /// </summary>
    public long TotalConnectionsCreated { get; set; }

    /// <summary>
    /// Total de conexões destruídas
    /// </summary>
    public long TotalConnectionsDestroyed { get; set; }

    /// <summary>
    /// Total de requisições de conexão
    /// </summary>
    public long TotalConnectionRequests { get; set; }

    /// <summary>
    /// Número de hits do pool
    /// </summary>
    public long PoolHits { get; set; }

    /// <summary>
    /// Número de misses do pool
    /// </summary>
    public long PoolMisses { get; set; }

    /// <summary>
    /// Taxa de hit do pool (%)
    /// </summary>
    public double PoolHitRate { get; set; }

    /// <summary>
    /// Timestamp da geração das estatísticas
    /// </summary>
    public DateTime GeneratedAt { get; set; }

    /// <summary>
    /// Detalhes por pool
    /// </summary>
    public Dictionary<string, OpcPoolStatistics> PoolDetails { get; set; } = new();
}

/// <summary>
/// Estatísticas de um pool específico
/// </summary>
public class OpcPoolStatistics
{
    /// <summary>
    /// Chave do pool
    /// </summary>
    public string PoolKey { get; set; } = string.Empty;

    /// <summary>
    /// Número total de conexões no pool
    /// </summary>
    public int TotalConnections { get; set; }

    /// <summary>
    /// Número de conexões ativas
    /// </summary>
    public int ActiveConnections { get; set; }

    /// <summary>
    /// Número de conexões idle
    /// </summary>
    public int IdleConnections { get; set; }

    /// <summary>
    /// Total de conexões criadas
    /// </summary>
    public long TotalConnectionsCreated { get; set; }

    /// <summary>
    /// Total de conexões destruídas
    /// </summary>
    public long TotalConnectionsDestroyed { get; set; }

    /// <summary>
    /// Total de requisições
    /// </summary>
    public long TotalConnectionRequests { get; set; }

    /// <summary>
    /// Hits do pool
    /// </summary>
    public long PoolHits { get; set; }

    /// <summary>
    /// Misses do pool
    /// </summary>
    public long PoolMisses { get; set; }

    /// <summary>
    /// Última atividade
    /// </summary>
    public DateTime LastActivity { get; set; }
}
