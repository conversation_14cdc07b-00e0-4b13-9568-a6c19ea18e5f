namespace AssetView.Nx.Core.Constants;

/// <summary>
/// Claims padrão do sistema
/// </summary>
public static class SystemClaims
{
    /// <summary>
    /// Claim para ID do tenant
    /// </summary>
    public const string TenantId = "tenant_id";

    /// <summary>
    /// Claim para nome do tenant
    /// </summary>
    public const string TenantName = "tenant_name";

    /// <summary>
    /// Claim para permissões do usuário
    /// </summary>
    public const string Permission = "permission";

    // Permissões de usuários
    public const string CanViewUsers = "users.view";
    public const string CanCreateUsers = "users.create";
    public const string CanEditUsers = "users.edit";
    public const string CanDeleteUsers = "users.delete";
    public const string CanManageUserRoles = "users.manage_roles";

    // Permissões de projetos
    public const string CanViewProjects = "projects.view";
    public const string CanCreateProjects = "projects.create";
    public const string CanEditProjects = "projects.edit";
    public const string CanDeleteProjects = "projects.delete";
    public const string CanManageProjectMembers = "projects.manage_members";

    // Permissões de tenant
    public const string CanViewTenantSettings = "tenant.view_settings";
    public const string CanEditTenantSettings = "tenant.edit_settings";
    public const string CanManageTenant = "tenant.manage";

    // Permissões do sistema
    public const string CanViewSystemSettings = "system.view_settings";
    public const string CanEditSystemSettings = "system.edit_settings";
    public const string CanManageSystem = "system.manage";

    /// <summary>
    /// Todas as permissões do sistema
    /// </summary>
    public static readonly string[] AllPermissions = 
    {
        CanViewUsers, CanCreateUsers, CanEditUsers, CanDeleteUsers, CanManageUserRoles,
        CanViewProjects, CanCreateProjects, CanEditProjects, CanDeleteProjects, CanManageProjectMembers,
        CanViewTenantSettings, CanEditTenantSettings, CanManageTenant,
        CanViewSystemSettings, CanEditSystemSettings, CanManageSystem
    };
}
