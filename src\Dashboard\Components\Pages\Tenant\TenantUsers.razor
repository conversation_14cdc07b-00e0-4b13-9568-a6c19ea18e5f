@page "/tenant/users"
@inherits AdminPageBase
@inject ITenantManagementService TenantService
@inject IUserManagementService UserService
@inject IDialogService DialogService
@inject ISnackbar Snackbar
@using AssetView.Nx.Modules.Tenants.Services
@using AssetView.Nx.Modules.Users.Services
@using AssetView.Nx.Core.Entities
@using AssetView.Nx.Dashboard.Components.Shared

<PageTitle>Usuários do Tenant - Smar Hart System</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge">
    <div class="d-flex justify-space-between align-center mb-4">
        <MudText Typo="Typo.h4">Usuários do Tenant</MudText>
        <MudButton Variant="Variant.Filled" Color="Color.Primary" StartIcon="@Icons.Material.Filled.PersonAdd" Href="/users/create">
            Novo Usuário
        </MudButton>
    </div>

    <!-- Informações do Tenant -->
    @if (_tenantLimits != null)
    {
        <MudCard Class="mb-4" Elevation="1">
            <MudCardContent>
                <MudGrid Justify="Justify.Center">
                    <MudItem xs="12" md="3">
                        <MudStack AlignItems="AlignItems.Center">
                            <MudText Typo="Typo.h6">@_tenantLimits.CurrentUsers</MudText>
                            <MudText Typo="Typo.caption">Usuários Atuais</MudText>
                        </MudStack>
                    </MudItem>
                    <MudItem xs="12" md="3">
                        <MudStack AlignItems="AlignItems.Center">
                            <MudText Typo="Typo.h6">@(_tenantLimits.MaxUsers?.ToString() ?? "Ilimitado")</MudText>
                            <MudText Typo="Typo.caption">Limite Máximo</MudText>
                        </MudStack>
                    </MudItem>
                    <MudItem xs="12" md="3">
                        <MudStack AlignItems="AlignItems.Center">
                            <MudText Typo="Typo.h6" Color="@(_tenantLimits.IsUserLimitReached ? Color.Error : Color.Success)">
                                @(_tenantLimits.IsUserLimitReached ? "Limite Atingido" : "Disponível")
                            </MudText>
                            <MudText Typo="Typo.caption">Status</MudText>
                        </MudStack>
                    </MudItem>
                    <MudItem xs="12" md="3">
                        @if (_tenantLimits.SubscriptionExpiresAt.HasValue)
                        {
                            <MudStack AlignItems="AlignItems.Center">
                                <MudText Typo="Typo.h6" Color="@(_tenantLimits.IsSubscriptionExpired ? Color.Error : Color.Primary)">
                                    @_tenantLimits.SubscriptionExpiresAt.Value.ToString("dd/MM/yyyy")
                                </MudText>
                                <MudText Typo="Typo.caption">Expira em</MudText>
                            </MudStack>
                        }
                    </MudItem>
                </MudGrid>
            </MudCardContent>
        </MudCard>
    }

    <!-- Filtros -->
    <MudCard Class="mb-4" Elevation="1">
        <MudCardContent>
            <MudGrid Justify="Justify.Center">
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="_searchTerm"
                                  Label="Buscar usuários"
                                  Variant="Variant.Outlined"
                                  Adornment="Adornment.Start"
                                  AdornmentIcon="@Icons.Material.Filled.Search"
                                  OnKeyDown="@(async (e) => { if (e.Key == "Enter") await SearchUsers(); })"
                                  Clearable="true"
                                  OnClearButtonClick="@(async () => { _searchTerm = string.Empty; await SearchUsers(); })" />
                </MudItem>
                <MudItem xs="12" md="3">
                    <MudSelect T="bool?" @bind-Value="_selectedStatus" Label="Status" Variant="Variant.Outlined" Clearable="true">
                        <MudSelectItem T="bool?" Value="true">Ativo</MudSelectItem>
                        <MudSelectItem T="bool?" Value="false">Inativo</MudSelectItem>
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" md="3">
                    <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="SearchUsers" FullWidth="true">
                        Buscar
                    </MudButton>
                </MudItem>
            </MudGrid>
        </MudCardContent>
    </MudCard>

    <!-- Lista de Usuários -->
    <MudCard Elevation="2">
        <MudCardContent Class="pa-0">
            @if (_loading)
            {
                <div class="d-flex justify-center pa-4">
                    <MudProgressCircular Indeterminate="true" />
                </div>
            }
            else if (!_users.Any())
            {
                <div class="d-flex flex-column align-center pa-8">
                    <MudIcon Icon="@Icons.Material.Filled.People" Size="Size.Large" Color="Color.Secondary" />
                    <MudText Typo="Typo.h6" Color="Color.Secondary" Class="mt-4">
                        @(_searchTerm.Length > 0 ? "Nenhum usuário encontrado" : "Nenhum usuário cadastrado")
                    </MudText>
                    @if (_searchTerm.Length == 0)
                    {
                        <MudButton Variant="Variant.Filled" Color="Color.Primary" Class="mt-4" Href="/users/create">
                            Criar primeiro usuário
                        </MudButton>
                    }
                </div>
            }
            else
            {
                <MudTable Items="_users" 
                         Hover="true" 
                         Breakpoint="Breakpoint.Sm" 
                         Loading="_loading"
                         LoadingProgressColor="Color.Info">
                    <HeaderContent>
                        <MudTh>Nome</MudTh>
                        <MudTh>Email</MudTh>
                        <MudTh>Usuário</MudTh>
                        <MudTh>Telefone</MudTh>
                        <MudTh>Status</MudTh>
                        <MudTh>Email Confirmado</MudTh>
                        <MudTh>Último Login</MudTh>
                        <MudTh>Criado em</MudTh>
                        <MudTh>Ações</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd DataLabel="Nome">
                            <div class="d-flex align-center">
                                <MudAvatar Color="Color.Primary" Size="Size.Small">
                                    @context.FullName.Substring(0, 1).ToUpper()
                                </MudAvatar>
                                <MudText Class="ml-2">@context.FullName</MudText>
                            </div>
                        </MudTd>
                        <MudTd DataLabel="Email">@context.Email</MudTd>
                        <MudTd DataLabel="Usuário">@context.UserName</MudTd>
                        <MudTd DataLabel="Telefone">@(context.PhoneNumber ?? "N/A")</MudTd>
                        <MudTd DataLabel="Status">
                            <MudChip T="string" Color="@(context.IsActive ? Color.Success : Color.Error)" Size="Size.Small">
                                @(context.IsActive ? "Ativo" : "Inativo")
                            </MudChip>
                        </MudTd>
                        <MudTd DataLabel="Email Confirmado">
                            <MudIcon Icon="@(context.EmailConfirmed ? Icons.Material.Filled.CheckCircle : Icons.Material.Filled.Cancel)" 
                                    Color="@(context.EmailConfirmed ? Color.Success : Color.Error)" />
                        </MudTd>
                        <MudTd DataLabel="Último Login">
                            @(context.LastLoginAt?.ToString("dd/MM/yyyy HH:mm") ?? "Nunca")
                        </MudTd>
                        <MudTd DataLabel="Criado em">
                            @context.CreatedAt.ToString("dd/MM/yyyy HH:mm")
                        </MudTd>
                        <MudTd DataLabel="Ações">
                            <MudButtonGroup Variant="Variant.Text" Size="Size.Small">
                                <MudIconButton Icon="@Icons.Material.Filled.Visibility" 
                                             Color="Color.Info" 
                                             Size="Size.Small"
                                             OnClick="@(() => ViewUser(context))"
                                             title="Visualizar" />
                                <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                             Color="Color.Primary" 
                                             Size="Size.Small"
                                             Href="@($"/users/edit/{context.Id}")"
                                             title="Editar" />
                                <MudIconButton Icon="@(context.IsActive ? Icons.Material.Filled.Block : Icons.Material.Filled.CheckCircle)" 
                                             Color="@(context.IsActive ? Color.Warning : Color.Success)" 
                                             Size="Size.Small"
                                             OnClick="@(() => ToggleUserStatus(context))"
                                             title="@(context.IsActive ? "Desativar" : "Ativar")" />
                            </MudButtonGroup>
                        </MudTd>
                    </RowTemplate>
                </MudTable>
            }
        </MudCardContent>
    </MudCard>

    <!-- Paginação -->
    @if (_pagedResult.TotalCount > 0)
    {
        <div class="d-flex justify-center mt-4">
            <MudPagination Count="@((_pagedResult.TotalCount + PageSize - 1) / PageSize)" 
                          Selected="_currentPage" 
                          SelectedChanged="OnPageChanged" />
        </div>
    }
</MudContainer>

@code {
    private List<ApplicationUser> _users = new();
    private PagedResult<ApplicationUser> _pagedResult = new();
    private TenantLimits? _tenantLimits;
    private bool _loading = true;
    private string _searchTerm = string.Empty;
    private bool? _selectedStatus;
    private int _currentPage = 1;
    private const int PageSize = 10;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        await Task.WhenAll(LoadUsers(), LoadTenantLimits());
    }

    private async Task LoadUsers()
    {
        _loading = true;
        try
        {
            _pagedResult = await TenantService.GetTenantUsersAsync(_currentPage, PageSize, _searchTerm);
            _users = _pagedResult.Items.ToList();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao carregar usuários: {ex.Message}", Severity.Error);
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task LoadTenantLimits()
    {
        try
        {
            _tenantLimits = await TenantService.GetTenantLimitsAsync();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao carregar limites do tenant: {ex.Message}", Severity.Error);
        }
    }

    private async Task SearchUsers()
    {
        _currentPage = 1;
        await LoadUsers();
    }

    private async Task OnPageChanged(int page)
    {
        _currentPage = page;
        await LoadUsers();
    }

    private async Task ToggleUserStatus(ApplicationUser user)
    {
        try
        {
            var result = await UserService.ToggleUserStatusAsync(user.Id);
            if (result.Success)
            {
                Snackbar.Add(result.Message ?? "Status alterado com sucesso!", Severity.Success);
                await LoadUsers();
            }
            else
            {
                Snackbar.Add(result.Message ?? "Erro ao alterar status", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro ao alterar status: {ex.Message}", Severity.Error);
        }
    }

    private void ViewUser(ApplicationUser user)
    {
        // Implementar visualização detalhada do usuário
        Snackbar.Add($"Visualizar usuário: {user.FullName}", Severity.Info);
    }
}
