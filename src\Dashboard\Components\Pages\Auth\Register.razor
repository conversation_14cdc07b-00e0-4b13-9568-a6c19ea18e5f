@page "/register"
@layout AuthLayout
@using AssetView.Nx.Modules.Users.Services
@using MudBlazor
@using System.ComponentModel.DataAnnotations
@inject IUserManagementService UserService
@inject ISnackbar Snackbar
@inject NavigationManager Navigation

<PageTitle>Registro - Smar Hart System</PageTitle>

<MudContainer MaxWidth="MaxWidth.Small" Class="mt-16">
    <MudCard Elevation="8" Class="pa-8">
        <MudCardContent>
            <div class="d-flex justify-center mb-6">
                <MudIcon Icon="@Icons.Material.Filled.AccountCircle" 
                         Color="Color.Primary" 
                         Size="Size.Large" 
                         Style="font-size: 4rem;" />
            </div>
            
            <MudText Typo="Typo.h4" Align="Align.Center" Class="mb-6">
                Criar Conta
            </MudText>
            
            <EditForm Model="@registerModel" OnValidSubmit="@HandleRegister">
                <DataAnnotationsValidator />
                
                <MudTextField @bind-Value="registerModel.FullName"
                              Label="Nome Completo"
                              Variant="Variant.Outlined"
                              Margin="Margin.Normal"
                              FullWidth="true"
                              Required="true"
                              For="@(() => registerModel.FullName)"
                              Class="mb-4" />
                
                <MudTextField @bind-Value="registerModel.Email"
                              Label="Email"
                              Variant="Variant.Outlined"
                              Margin="Margin.Normal"
                              FullWidth="true"
                              Required="true"
                              InputType="InputType.Email"
                              For="@(() => registerModel.Email)"
                              Class="mb-4" />
                
                <MudTextField @bind-Value="registerModel.UserName"
                              Label="Nome de Usuário"
                              Variant="Variant.Outlined"
                              Margin="Margin.Normal"
                              FullWidth="true"
                              Required="true"
                              For="@(() => registerModel.UserName)"
                              Class="mb-4" />
                
                <MudTextField @bind-Value="registerModel.Password"
                              Label="Senha"
                              Variant="Variant.Outlined"
                              Margin="Margin.Normal"
                              FullWidth="true"
                              Required="true"
                              InputType="@passwordInputType"
                              Adornment="Adornment.End"
                              AdornmentIcon="@passwordIcon"
                              OnAdornmentClick="@TogglePasswordVisibility"
                              For="@(() => registerModel.Password)"
                              Class="mb-4" />
                
                <MudTextField @bind-Value="registerModel.ConfirmPassword"
                              Label="Confirmar Senha"
                              Variant="Variant.Outlined"
                              Margin="Margin.Normal"
                              FullWidth="true"
                              Required="true"
                              InputType="@passwordInputType"
                              For="@(() => registerModel.ConfirmPassword)"
                              Class="mb-6" />
                
                <MudButton ButtonType="ButtonType.Submit"
                           Variant="Variant.Filled"
                           Color="Color.Primary"
                           FullWidth="true"
                           Size="Size.Large"
                           Disabled="@isLoading"
                           Class="mb-4">
                    @if (isLoading)
                    {
                        <MudProgressCircular Size="Size.Small" Indeterminate="true" Class="mr-2" />
                        <span>Criando conta...</span>
                    }
                    else
                    {
                        <span>Criar Conta</span>
                    }
                </MudButton>
            </EditForm>
            
            <MudDivider Class="my-4" />
            
            <div class="d-flex justify-center">
                <MudText Typo="Typo.body2">
                    Já tem uma conta?
                    <MudLink Href="/login" Color="Color.Primary">
                        Fazer Login
                    </MudLink>
                </MudText>
            </div>
        </MudCardContent>
    </MudCard>
</MudContainer>

@code {
    private RegisterModel registerModel = new();
    private bool isLoading = false;
    private bool showPassword = false;
    private InputType passwordInputType = InputType.Password;
    private string passwordIcon = Icons.Material.Filled.VisibilityOff;

    private void TogglePasswordVisibility()
    {
        if (showPassword)
        {
            showPassword = false;
            passwordInputType = InputType.Password;
            passwordIcon = Icons.Material.Filled.VisibilityOff;
        }
        else
        {
            showPassword = true;
            passwordInputType = InputType.Text;
            passwordIcon = Icons.Material.Filled.Visibility;
        }
    }

    private async Task HandleRegister()
    {
        if (isLoading) return;

        try
        {
            isLoading = true;

            if (registerModel.Password != registerModel.ConfirmPassword)
            {
                Snackbar.Add("As senhas não coincidem", Severity.Error);
                return;
            }

            var request = new CreateUserRequest
            {
                FullName = registerModel.FullName,
                Email = registerModel.Email,
                UserName = registerModel.UserName,
                Password = registerModel.Password,
                IsActive = true
            };

            var result = await UserService.CreateUserAsync(request);

            if (result.Success)
            {
                Snackbar.Add("Conta criada com sucesso! Você pode fazer login agora.", Severity.Success);
                Navigation.NavigateTo("/login");
            }
            else
            {
                Snackbar.Add(result.Message ?? "Erro ao criar conta", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"Erro inesperado: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoading = false;
        }
    }

    public class RegisterModel
    {
        [Required(ErrorMessage = "Nome completo é obrigatório")]
        public string FullName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Email é obrigatório")]
        [EmailAddress(ErrorMessage = "Email inválido")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "Nome de usuário é obrigatório")]
        public string UserName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Senha é obrigatória")]
        [MinLength(6, ErrorMessage = "Senha deve ter pelo menos 6 caracteres")]
        public string Password { get; set; } = string.Empty;

        [Required(ErrorMessage = "Confirmação de senha é obrigatória")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }
}
