using AssetView.Nx.Core.Entities;
using AssetView.Nx.Core.Enums;
using AssetView.Nx.Core.Interfaces;
using AssetView.Nx.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace AssetView.Nx.Infrastructure.Services;

/// <summary>
/// Serviço para gerenciamento de tenants
/// </summary>
public class TenantService : ITenantService
{
    private readonly ApplicationDbContext _context;
    private readonly IConfiguration _configuration;
    private readonly ITenantContextFactory? _tenantContextFactory;
    private string? _currentTenantId;

    public TenantService(ApplicationDbContext context, IConfiguration configuration, ITenantContextFactory? tenantContextFactory)
    {
        _context = context;
        _configuration = configuration;
        _tenantContextFactory = tenantContextFactory;

    }

    public async Task<Tenant?> GetCurrentTenantAsync()
    {
        if (string.IsNullOrEmpty(_currentTenantId))
            return null;

        return await _context.Tenants
            .FirstOrDefaultAsync(t => t.Id == _currentTenantId);
    }

    public string? GetCurrentTenantId()
    {
        //return _currentTenantId;
        return _tenantContextFactory?.GetCurrentTenantId();
    }

    public void SetCurrentTenant(string tenantId)
    {
        _currentTenantId = tenantId;
        _tenantContextFactory?.SetCurrentTenantId(tenantId);
    }

    public async Task<Tenant?> GetTenantBySubdomainAsync(string subdomain)
    {
        return await _context.Tenants
            .FirstOrDefaultAsync(t => t.Subdomain == subdomain && t.IsActive);
    }

    public async Task<Tenant?> GetTenantByIdAsync(string tenantId)
    {
        return await _context.Tenants
            .FirstOrDefaultAsync(t => t.Id == tenantId);
    }

    public async Task<bool> IsTenantActiveAsync(string tenantId)
    {
        var tenant = await GetTenantByIdAsync(tenantId);
        return tenant?.IsActive == true;
    }

    public async Task<string> GetTenantConnectionStringAsync(string tenantId)
    {
        var tenant = await GetTenantByIdAsync(tenantId);
        
        if (tenant == null)
            throw new InvalidOperationException($"Tenant {tenantId} not found");

        // Se o tenant usa banco separado, retorna sua connection string
        if (tenant.DatabaseStrategy == TenantDatabaseStrategy.Separate && 
            !string.IsNullOrEmpty(tenant.ConnectionString))
        {
            return tenant.ConnectionString;
        }

        // Caso contrário, retorna a connection string padrão
        return _configuration.GetConnectionString("DefaultConnection") 
               ?? throw new InvalidOperationException("Default connection string not found");
    }
}
